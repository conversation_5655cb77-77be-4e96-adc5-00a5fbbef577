//
//  LayoutConstants.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/29.
//

import SwiftUI

// MARK: - 全局布局常量
struct LayoutConstants {
    
    // MARK: - 页边距
    /// 标准水平页边距（与系统List默认边距一致）
    static let standardHorizontalPadding: CGFloat = 20
    
    /// 标准垂直页边距
    static let standardVerticalPadding: CGFloat = 16
    
    /// List行的标准内边距
    static let standardListRowInsets = EdgeInsets(
        top: 4,
        leading: standardHorizontalPadding,
        bottom: 4,
        trailing: standardHorizontalPadding
    )
    
    // MARK: - 圆角
    /// 标准圆角半径
    static let standardCornerRadius: CGFloat = 12
    
    /// 大圆角半径
    static let largeCornerRadius: CGFloat = 16
    
    // MARK: - 间距
    /// 标准元素间距
    static let standardSpacing: CGFloat = 16
    
    /// 小间距
    static let smallSpacing: CGFloat = 8
    
    /// 大间距
    static let largeSpacing: CGFloat = 24
    
    // MARK: - 阴影
    /// 标准阴影
    static let standardShadow = (
        color: Color.black.opacity(0.1),
        radius: CGFloat(12),
        x: CGFloat(0),
        y: CGFloat(4)
    )
}

// MARK: - SwiftUI扩展
extension View {
    /// 应用标准水平页边距
    func standardHorizontalPadding() -> some View {
        self.padding(.horizontal, LayoutConstants.standardHorizontalPadding)
    }
    
    /// 应用标准垂直页边距
    func standardVerticalPadding() -> some View {
        self.padding(.vertical, LayoutConstants.standardVerticalPadding)
    }
    
    /// 应用标准页边距
    func standardPadding() -> some View {
        self.padding(.horizontal, LayoutConstants.standardHorizontalPadding)
            .padding(.vertical, LayoutConstants.standardVerticalPadding)
    }
    
    /// 应用标准圆角
    func standardCornerRadius() -> some View {
        self.cornerRadius(LayoutConstants.standardCornerRadius)
    }
    
    /// 应用大圆角
    func largeCornerRadius() -> some View {
        self.cornerRadius(LayoutConstants.largeCornerRadius)
    }
    
    /// 应用标准阴影
    func standardShadow() -> some View {
        self.shadow(
            color: LayoutConstants.standardShadow.color,
            radius: LayoutConstants.standardShadow.radius,
            x: LayoutConstants.standardShadow.x,
            y: LayoutConstants.standardShadow.y
        )
    }
}
