//
//  LocalizationHelper.swift
//  DianDian
//
//  Created by Assistant on 2025/7/30.
//

import SwiftUI
import Foundation



// MARK: - 本地化辅助函数
class LocalizationHelper {
    static let shared = LocalizationHelper()

    private init() {
    }

    /// 获取本地化字符串
    /// - Parameter key: 本地化键值
    /// - Returns: 本地化后的字符串
    static func localizedString(_ key: String) -> String {
        // 直接使用系统的NSLocalizedString，应用重启后会使用新语言
        return NSLocalizedString(key, comment: "")
    }

    /// 获取带参数的本地化字符串
    /// - Parameters:
    ///   - key: 本地化键值
    ///   - arguments: 参数列表
    /// - Returns: 本地化后的字符串
    static func localizedString(_ key: String, _ arguments: CVarArg...) -> String {
        let format = localizedString(key)
        return String(format: format, arguments: arguments)
    }
}

// MARK: - String 扩展
extension String {
    /// 本地化字符串
    var localized: String {
        return LocalizationHelper.localizedString(self)
    }

    /// 带参数的本地化字符串
    func localized(_ arguments: CVarArg...) -> String {
        let format = LocalizationHelper.localizedString(self)
        return String(format: format, arguments: arguments)
    }
}