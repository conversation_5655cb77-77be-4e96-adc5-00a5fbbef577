//
//  EventSwiftData.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/29.
//

import Foundation
import SwiftData
import SwiftUI

@Model
class EventSwiftData {
    var id: String = UUID().uuidString
    var title: String = ""
    var date: String = ""
    var cycleType: Int = 0 // 默认为永不重复
    var notificationTime: String = "09:00"
    var isLunar: Bool = false
    var note: String = ""
    var color: String = "blue"
    var iconName: String = "heart.fill"
    var backgroundImageName: String = ""
    var isNotificationEnabled: Bool = true
    var advanceNotificationType: Int = 0
    var isArchived: Bool = false
    var createdAt: Date = Date()
    var updatedAt: Date = Date()
    
    init() {}
    
    init(title: String, date: String) {
        self.title = title
        self.date = date
    }
    
    // MARK: - 计算属性
    
    /// 事件日期对象
    var eventDate: Date? {
        let formatter = createISODateFormatter()
        return formatter.date(from: date)
    }

    private func createISODateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone.current
        return formatter
    }
    
    /// 下次发生日期
    var nextOccurrenceDate: Date? {
        guard let eventDate = eventDate else { return nil }

        // 如果是农历日期，暂时返回nil（需要LunarCalendarManager）
        if isLunar {
            // TODO: 实现农历计算逻辑
            return nil
        }

        let calendar = Calendar.current
        let now = Date()

        // 处理不同的循环类型
        switch cycleTypeEnum {
        case .never:
            // 永不重复：只返回原始事件日期（如果还没过期）
            if eventDate >= calendar.startOfDay(for: now) {
                return eventDate
            } else {
                return nil // 已过期的一次性事件不再显示
            }

        case .yearly:
            let currentYear = calendar.component(.year, from: now)

            // 获取今年的事件日期
            var components = calendar.dateComponents([.month, .day], from: eventDate)
            components.year = currentYear

            guard let thisYearDate = calendar.date(from: components) else { return nil }

            // 如果今年的日期还没过，返回今年的日期
            if thisYearDate >= calendar.startOfDay(for: now) {
                return thisYearDate
            } else {
                // 否则返回明年的日期
                components.year = currentYear + 1
                return calendar.date(from: components)
            }

        case .monthly, .weekly, .daily:
            // 其他循环类型的处理逻辑（暂时保持简单实现）
            return eventDate
        }
    }

    /// 专门用于通知计算的下次发生日期（考虑通知时间）
    var nextOccurrenceDateForNotification: Date? {
        guard let eventDate = eventDate else { return nil }

        // 如果是农历日期，使用农历计算
        if isLunar {
            // 农历事件使用特殊的通知逻辑在NotificationManager中处理
            return nextOccurrenceDate
        }

        let calendar = Calendar.current
        let now = Date()

        // 解析通知时间
        let timeComponents = notificationTime.split(separator: ":")
        guard timeComponents.count == 2,
              let hour = Int(timeComponents[0]),
              let minute = Int(timeComponents[1]) else {
            // 如果通知时间解析失败，使用普通逻辑
            return nextOccurrenceDate
        }

        // 处理不同的循环类型
        switch cycleTypeEnum {
        case .never:
            // 永不重复：计算原始事件日期的通知时间
            guard let eventNotificationTime = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: eventDate) else { return nil }

            // 如果通知时间还没过，返回原始事件日期
            if eventNotificationTime > now {
                return eventDate
            } else {
                return nil // 已过期的一次性事件不再安排通知
            }

        case .yearly:
            let currentYear = calendar.component(.year, from: now)

            // 获取今年的事件日期
            var components = calendar.dateComponents([.month, .day], from: eventDate)
            components.year = currentYear

            guard let thisYearDate = calendar.date(from: components) else { return nil }

            // 计算今年的具体通知时间
            guard let thisYearNotificationTime = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: thisYearDate) else { return nil }

            // 如果今年的通知时间还没过，返回今年的日期
            if thisYearNotificationTime > now {
                return thisYearDate
            } else {
                // 否则返回明年的日期
                components.year = currentYear + 1
                return calendar.date(from: components)
            }

        case .monthly, .weekly, .daily:
            // 其他循环类型的处理逻辑（暂时保持简单实现）
            return eventDate
        }
    }

    /// 距离下次发生的天数
    var daysUntilNext: Int {
        // 对于永不重复的事件，计算与原始事件日期的天数差
        if cycleTypeEnum == .never {
            guard let eventDate = eventDate else { return -1 }

            let calendar = Calendar.current
            let now = calendar.startOfDay(for: Date())
            let targetDate = calendar.startOfDay(for: eventDate)

            let components = calendar.dateComponents([.day], from: now, to: targetDate)
            return components.day ?? -1
        }

        // 对于重复事件，计算与下次发生日期的天数差
        guard let nextDate = nextOccurrenceDate else { return -1 }

        let calendar = Calendar.current
        let now = calendar.startOfDay(for: Date())
        let targetDate = calendar.startOfDay(for: nextDate)

        let components = calendar.dateComponents([.day], from: now, to: targetDate)
        return components.day ?? -1
    }
    
    /// 是否是今天
    func isToday() -> Bool {
        return daysUntilNext == 0
    }
    
    /// 格式化的剩余天数文本
    var daysUntilNextText: String {
        let days = daysUntilNext

        if days == 0 {
            return "今天"
        } else if days == 1 {
            return "明天"
        } else if days > 0 {
            return "还有 \(days) 天"
        } else {
            // 对于永不重复的事件，显示已过天数
            if cycleTypeEnum == .never {
                return "已过 \(abs(days)) 天"
            } else {
                return "已过期"
            }
        }
    }
    
    /// 事件颜色
    var eventColor: Color {
        switch color {
        case "red": return .red
        case "blue": return .blue
        case "green": return .green
        case "orange": return .orange
        case "purple": return .purple
        case "pink": return .pink
        case "yellow": return .yellow
        case "indigo": return .indigo
        case "teal": return .teal
        case "mint": return .mint
        case "cyan": return .cyan
        case "brown": return .brown
        default: return .blue
        }
    }
    
    /// 通知时间对象
    var notificationTimeDate: Date {
        let formatter = createHourMinuteFormatter()
        return formatter.date(from: notificationTime) ?? Date()
    }

    private func createHourMinuteFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone.current
        return formatter
    }
    
    /// 提前通知类型枚举
    var advanceNotificationTypeEnum: EventAdvanceNotificationType {
        return EventAdvanceNotificationType(rawValue: advanceNotificationType) ?? .none
    }
    
    /// 循环类型枚举
    var cycleTypeEnum: EventCycleType {
        return EventCycleType(rawValue: cycleType) ?? .never
    }

    /// 格式化的下次发生日期（Full格式）
    var formattedNextDateShort: String {
        // 对于永不重复的事件，如果已过期，显示原始日期
        if cycleTypeEnum == .never {
            guard let eventDate = eventDate else { return "未知日期" }

            if isLunar {
                // 农历事件显示：暂时显示公历日期（需要LunarCalendarManager）
                let fullDateFormatter = createFullDateFormatter()
                return fullDateFormatter.string(from: eventDate)
            } else {
                let fullDateFormatter = createFullDateFormatter()
                return fullDateFormatter.string(from: eventDate)
            }
        }

        // 对于重复事件，显示下次发生日期
        guard let nextDate = nextOccurrenceDate else { return "未知日期" }

        if isLunar {
            // 农历事件显示：暂时显示公历日期（需要LunarCalendarManager）
            let fullDateFormatter = createFullDateFormatter()
            return fullDateFormatter.string(from: nextDate)
        } else {
            let fullDateFormatter = createFullDateFormatter()
            return fullDateFormatter.string(from: nextDate)
        }
    }

    private func createFullDateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale.current
        return formatter
    }
}

// MARK: - 枚举定义

enum EventCycleType: Int, CaseIterable {
    case never = 0
    case yearly = 1
    case monthly = 2
    case weekly = 3
    case daily = 4

    var displayName: String {
        switch self {
        case .never: return "永不"
        case .yearly: return "每年"
        case .monthly: return "每月"
        case .weekly: return "每周"
        case .daily: return "每天"
        }
    }
}

enum EventAdvanceNotificationType: Int, CaseIterable {
    case none = -1
    case onTime = 0
    case oneDay = 1
    case threeDays = 3
    case oneWeek = 7

    var displayName: String {
        switch self {
        case .none: return "不提醒"
        case .onTime: return "准时提醒"
        case .oneDay: return "提前1天"
        case .threeDays: return "提前3天"
        case .oneWeek: return "提前1周"
        }
    }

    /// 获取启用通知时的可用选项（排除"不提醒"）
    static var enabledNotificationCases: [EventAdvanceNotificationType] {
        return [.onTime, .oneDay, .threeDays, .oneWeek]
    }
}
