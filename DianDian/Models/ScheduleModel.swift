//
//  ScheduleModel.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/28.
//

import Foundation
import EventKit
import SwiftUI
import UIKit

// MARK: - 日程数据模型
struct ScheduleModel: Identifiable {
    let id: String
    let title: String
    let startDate: Date
    let endDate: Date
    let isAllDay: Bool
    let location: String?
    let notes: String?
    let calendar: String
    let calendarColor: String
    
    // MARK: - 计算属性
    
    /// 格式化的开始时间
    var formattedStartTime: String {
        if isAllDay {
            return NSLocalizedString("全天", comment: "All day event")
        } else {
            let formatter = createTimeFormatter()
            return formatter.string(from: startDate)
        }
    }

    /// 格式化的结束时间
    var formattedEndTime: String {
        if isAllDay {
            return ""
        } else {
            let formatter = createTimeFormatter()
            return formatter.string(from: endDate)
        }
    }

    private func createTimeFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale.current
        return formatter
    }
    
    /// 时间段文本
    var timeRangeText: String {
        if isAllDay {
            return "全天"
        } else {
            let startFormatter = DateFormatter()
            startFormatter.dateFormat = "h:mm a"
            let endFormatter = DateFormatter()
            endFormatter.dateFormat = "h:mm a"
            
            return "\(startFormatter.string(from: startDate))\n\(endFormatter.string(from: endDate))"
        }
    }
    
    /// 持续时间文本
    var durationText: String {
        if isAllDay {
            let calendar = Calendar.current
            let days = calendar.dateComponents([.day], from: startDate, to: endDate).day ?? 0
            return days > 0 ? "\(days + 1)天" : "全天"
        } else {
            let duration = endDate.timeIntervalSince(startDate)
            let hours = Int(duration) / 3600
            let minutes = Int(duration) % 3600 / 60
            
            if hours > 0 {
                return minutes > 0 ? "\(hours)h \(minutes)m" : "\(hours)h 00m"
            } else {
                return "\(minutes)m"
            }
        }
    }
    
    /// 时间段类型
    var timeOfDay: String {
        if isAllDay {
            return "全天"
        }
        
        let hour = Calendar.current.component(.hour, from: startDate)
        switch hour {
        case 5..<12:
            return "Morning"
        case 12..<17:
            return "Afternoon"
        case 17..<21:
            return "Evening"
        default:
            return "Night"
        }
    }
    
    /// 是否是今天的日程
    var isToday: Bool {
        Calendar.current.isDateInToday(startDate)
    }
    
    /// 是否正在进行中
    var isOngoing: Bool {
        let now = Date()
        return startDate <= now && now <= endDate
    }

    /// 是否已结束
    var isFinished: Bool {
        let now = Date()
        return endDate < now
    }

    /// 获取主题色，从日历颜色转换为SwiftUI Color
    var themeColor: Color {
        if let color = Color(hex: calendarColor) {
            return color
        }
        return Color.blue // 默认蓝色
    }
}

// MARK: - 从EKEvent创建ScheduleModel的扩展
extension ScheduleModel {
    init(from event: EKEvent) {
        self.id = event.eventIdentifier ?? UUID().uuidString
        self.title = event.title ?? "无标题事件"
        self.startDate = event.startDate
        self.endDate = event.endDate
        self.isAllDay = event.isAllDay
        self.location = event.location
        self.notes = event.notes
        self.calendar = event.calendar?.title ?? "默认日历"
        
        // 获取日历颜色
        if let cgColor = event.calendar?.cgColor {
            let color = UIColor(cgColor: cgColor)
            var red: CGFloat = 0
            var green: CGFloat = 0
            var blue: CGFloat = 0
            var alpha: CGFloat = 0
            color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
            
            self.calendarColor = String(format: "#%02X%02X%02X", 
                                      Int(red * 255), 
                                      Int(green * 255), 
                                      Int(blue * 255))
        } else {
            self.calendarColor = "#007AFF" // 默认蓝色
        }
    }
}

// MARK: - 日程分组辅助结构
struct ScheduleGroup {
    let date: Date
    let schedules: [ScheduleModel]
    
    var formattedDate: String {
        if Calendar.current.isDateInToday(date) {
            return NSLocalizedString("今天", comment: "Today")
        } else if Calendar.current.isDateInTomorrow(date) {
            return NSLocalizedString("明天", comment: "Tomorrow")
        } else {
            let fullDateFormatter = createFullDateFormatter()
            return fullDateFormatter.string(from: date)
        }
    }

    private func createFullDateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale.current
        return formatter
    }
}
