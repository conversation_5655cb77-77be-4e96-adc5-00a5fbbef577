//
//  WidgetEventData.swift
//  DianDian
//
//  Created by LeeHom on 2025/8/3.
//

import Foundation

// MARK: - Widget Event Data
struct WidgetEventData: Identifiable {
    let id: String
    let title: String
    let date: String
    let iconName: String
    let color: String
    let daysUntilNext: Int
    let isLunar: Bool
    
    init(id: String, title: String, date: String, iconName: String, color: String, daysUntilNext: Int, isLunar: Bool) {
        self.id = id
        self.title = title
        self.date = date
        self.iconName = iconName
        self.color = color
        self.daysUntilNext = daysUntilNext
        self.isLunar = isLunar
    }
    
    init(from event: EventSwiftData) {
        self.id = event.id
        self.title = event.title
        self.date = event.date
        self.iconName = event.iconName
        self.color = event.color
        self.daysUntilNext = event.daysUntilNext
        self.isLunar = event.isLunar
    }
}
