//
//  SecurityManager.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/28.
//

import Foundation
import SwiftUI

// MARK: - 安全管理器
class SecurityManager: ObservableObject {
    @Published var isSecurityEnabled: Bool = false
    @Published var isAuthenticated: Bool = false
    
    private let securityEnabledKey = "SecurityEnabled"
    private let securityCodeKey = "SecurityCode"
    
    init() {
        loadSecuritySettings()
    }
    
    // MARK: - 加载安全设置
    private func loadSecuritySettings() {
        isSecurityEnabled = UserDefaults.standard.bool(forKey: securityEnabledKey)
        // 如果启用了安全功能，默认为未认证状态
        if isSecurityEnabled {
            isAuthenticated = false
        } else {
            isAuthenticated = true
        }
    }
    
    // MARK: - 启用安全功能
    func enableSecurity(with code: String) {
        guard code.count == 6, code.allSatisfy({ $0.isNumber }) else { return }
        
        UserDefaults.standard.set(true, forKey: securityEnabledKey)
        UserDefaults.standard.set(code, forKey: securityCodeKey)
        isSecurityEnabled = true
        isAuthenticated = true
    }
    
    // MARK: - 禁用安全功能
    func disableSecurity() {
        UserDefaults.standard.set(false, forKey: securityEnabledKey)
        UserDefaults.standard.removeObject(forKey: securityCodeKey)
        isSecurityEnabled = false
        isAuthenticated = true
    }
    
    // MARK: - 验证安全码
    func authenticate(with code: String) -> Bool {
        guard let savedCode = UserDefaults.standard.string(forKey: securityCodeKey) else {
            return false
        }
        
        if code == savedCode {
            isAuthenticated = true
            return true
        }
        return false
    }
    
    // MARK: - 修改安全码
    func changeSecurityCode(oldCode: String, newCode: String) -> Bool {
        guard let savedCode = UserDefaults.standard.string(forKey: securityCodeKey) else {
            return false
        }
        
        if oldCode == savedCode && newCode.count == 6 && newCode.allSatisfy({ $0.isNumber }) {
            UserDefaults.standard.set(newCode, forKey: securityCodeKey)
            return true
        }
        return false
    }
    
    // MARK: - 应用进入后台时重置认证状态
    func resetAuthenticationOnBackground() {
        if isSecurityEnabled {
            isAuthenticated = false
        }
    }
    
    // MARK: - 检查是否有保存的安全码
    func hasSecurityCode() -> Bool {
        return UserDefaults.standard.string(forKey: securityCodeKey) != nil
    }
}
