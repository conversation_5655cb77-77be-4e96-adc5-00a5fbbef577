//
//  AppStateManager.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import SwiftUI
import Combine

// MARK: - 应用状态管理器
class AppStateManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isFirstLaunch: Bool = true
    @Published var isPremiumUser: Bool = false
    @Published var currentLanguage: String = "zh-Hans"
    @Published var notificationPermissionGranted: Bool = false
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - User Settings
    @Published var enableNotifications: Bool = true
    @Published var defaultNotificationTime: Date = Calendar.current.date(from: DateComponents(hour: 9, minute: 0)) ?? Date()
    @Published var enableiCloudSync: Bool = true
    @Published var enableHapticFeedback: Bool = true
    
    // MARK: - App Configuration
    private let userDefaults = UserDefaults.standard
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Constants
    struct UserDefaultsKeys {
        static let isFirstLaunch = "IsFirstLaunch"
        static let isPremiumUser = "IsPremiumUser"
        static let currentLanguage = "CurrentLanguage"
        static let enableNotifications = "EnableNotifications"
        static let defaultNotificationTime = "DefaultNotificationTime"
        static let enableiCloudSync = "EnableiCloudSync"
        static let enableHapticFeedback = "EnableHapticFeedback"
    }
    
    // MARK: - Initialization
    init() {
        loadUserSettings()
        setupObservers()
    }
    
    // MARK: - Public Methods
    
    /// 初始化应用
    func initializeApp() {
        // 检查是否首次启动
        isFirstLaunch = !userDefaults.bool(forKey: UserDefaultsKeys.isFirstLaunch)
        
        // 加载用户设置
        loadUserSettings()
    }
    
    /// 完成引导流程
    func completeOnboarding() {
        isFirstLaunch = false
        userDefaults.set(true, forKey: UserDefaultsKeys.isFirstLaunch)
    }
    
    /// 购买高级版
    func purchasePremium() {
        isPremiumUser = true
        saveUserSettings()
    }
    
    /// 恢复购买
    func restorePurchase() -> Bool {
        // 这里应该调用 StoreKit 的恢复购买逻辑
        // 暂时返回当前状态
        return isPremiumUser
    }
    
    /// 切换语言
    func changeLanguage(to language: String) {
        currentLanguage = language
        saveUserSettings()
    }
    
    /// 显示错误消息
    func showError(_ message: String) {
        errorMessage = message
        
        // 3秒后自动清除错误消息
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.errorMessage = nil
        }
    }
    
    /// 清除错误消息
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - Private Methods
    
    private func loadUserSettings() {
        isPremiumUser = userDefaults.bool(forKey: UserDefaultsKeys.isPremiumUser)
        currentLanguage = userDefaults.string(forKey: UserDefaultsKeys.currentLanguage) ?? "zh-Hans"
        enableNotifications = userDefaults.bool(forKey: UserDefaultsKeys.enableNotifications)
        enableiCloudSync = userDefaults.bool(forKey: UserDefaultsKeys.enableiCloudSync)
        enableHapticFeedback = userDefaults.bool(forKey: UserDefaultsKeys.enableHapticFeedback)
        
        if let timeData = userDefaults.data(forKey: UserDefaultsKeys.defaultNotificationTime),
           let time = try? JSONDecoder().decode(Date.self, from: timeData) {
            defaultNotificationTime = time
        }
    }
    
    private func saveUserSettings() {
        userDefaults.set(isPremiumUser, forKey: UserDefaultsKeys.isPremiumUser)
        userDefaults.set(currentLanguage, forKey: UserDefaultsKeys.currentLanguage)
        userDefaults.set(enableNotifications, forKey: UserDefaultsKeys.enableNotifications)
        userDefaults.set(enableiCloudSync, forKey: UserDefaultsKeys.enableiCloudSync)
        userDefaults.set(enableHapticFeedback, forKey: UserDefaultsKeys.enableHapticFeedback)
        
        if let timeData = try? JSONEncoder().encode(defaultNotificationTime) {
            userDefaults.set(timeData, forKey: UserDefaultsKeys.defaultNotificationTime)
        }
    }
    
    private func setupObservers() {
        // 监听设置变化并自动保存
        Publishers.CombineLatest4(
            $isPremiumUser,
            $currentLanguage,
            $enableNotifications,
            $enableiCloudSync
        )
        .dropFirst() // 忽略初始值
        .sink { [weak self] _, _, _, _ in
            self?.saveUserSettings()
        }
        .store(in: &cancellables)
        
        $enableHapticFeedback
            .dropFirst()
            .sink { [weak self] _ in
                self?.saveUserSettings()
            }
            .store(in: &cancellables)
        
        $defaultNotificationTime
            .dropFirst()
            .sink { [weak self] _ in
                self?.saveUserSettings()
            }
            .store(in: &cancellables)
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let languageChanged = Notification.Name("LanguageChanged")
    static let premiumStatusChanged = Notification.Name("PremiumStatusChanged")
}

// MARK: - App Configuration
extension AppStateManager {
    
    /// 应用版本信息
    var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }
    
    /// 构建版本
    var buildNumber: String {
        Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
    }
    
    /// 应用名称
    var appName: String {
        Bundle.main.infoDictionary?["CFBundleDisplayName"] as? String ?? "点点纪念"
    }
    
    /// 免费版事件数量限制
    var freeEventLimit: Int {
        return 5
    }
    
    /// 是否可以添加更多事件（包含已归档的事件）
    func canAddMoreEvents(activeCount: Int, archivedCount: Int) -> Bool {
        let totalCount = activeCount + archivedCount
        return isPremiumUser || totalCount < freeEventLimit
    }
}
