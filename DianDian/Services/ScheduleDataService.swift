//
//  ScheduleDataService.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/28.
//

import Foundation
@preconcurrency import EventKit
import SwiftUI

// MARK: - 日程数据服务
@MainActor
class ScheduleDataService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var schedules: [ScheduleModel] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var calendarAccessGranted: Bool = false
    
    // MARK: - Private Properties
    private let eventStore = EKEventStore()
    
    // MARK: - Initialization
    init() {
        checkCalendarAccess()
    }
    
    // MARK: - 检查日历权限
    private func checkCalendarAccess() {
        switch EKEventStore.authorizationStatus(for: .event) {
        case .authorized:
            calendarAccessGranted = true
        case .fullAccess:
            calendarAccessGranted = true
        case .writeOnly:
            calendarAccessGranted = false
            errorMessage = "需要完整日历权限来读取日程信息"
        case .notDetermined:
            requestCalendarAccess()
        case .denied, .restricted:
            calendarAccessGranted = false
            errorMessage = "需要日历权限来读取日程信息"
        @unknown default:
            calendarAccessGranted = false
        }
    }
    
    // MARK: - 请求日历权限
    private func requestCalendarAccess() {
        if #available(iOS 17.0, *) {
            eventStore.requestFullAccessToEvents { [weak self] granted, error in
                DispatchQueue.main.async {
                    self?.calendarAccessGranted = granted
                    if !granted {
                        self?.errorMessage = "需要日历权限来显示日程信息"
                        self?.schedules = []
                    } else {
                        self?.errorMessage = nil
                        self?.loadSchedules()
                    }
                }
            }
        } else {
            eventStore.requestAccess(to: .event) { [weak self] granted, error in
                DispatchQueue.main.async {
                    self?.calendarAccessGranted = granted
                    if !granted {
                        self?.errorMessage = "需要日历权限来显示日程信息"
                        self?.schedules = []
                    } else {
                        self?.errorMessage = nil
                        self?.loadSchedules()
                    }
                }
            }
        }
    }
    
    // MARK: - 加载日程数据
    func loadSchedules(year: Int? = nil) {
        guard calendarAccessGranted else {
            schedules = []
            errorMessage = "需要日历权限来显示日程信息"
            return
        }

        isLoading = true
        errorMessage = nil

        Task {
            await loadSchedulesAsync(year: year)
        }
    }

    // MARK: - 异步加载日程数据
    private func loadSchedulesAsync(year: Int?) async {
        let calendar = Calendar.current
        let currentYear = year ?? calendar.component(.year, from: Date())

        // 获取整年的日程数据
        let startDate = calendar.date(from: DateComponents(year: currentYear, month: 1, day: 1)) ?? Date()
        let endDate = calendar.date(from: DateComponents(year: currentYear + 1, month: 1, day: 1)) ?? Date()

        // 获取所有日历
        let calendars = eventStore.calendars(for: .event)

        // 创建谓词来查找日程事件
        let predicate = eventStore.predicateForEvents(withStart: startDate, end: endDate, calendars: calendars)

        // 在后台线程执行事件查询
        let events = await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async { [eventStore] in
                let events = eventStore.events(matching: predicate)
                continuation.resume(returning: events)
            }
        }

        // 过滤掉节日类型的事件，只保留日程
        let scheduleEvents = events.filter { event in
            !self.isHolidayEvent(event)
        }

        // 转换为ScheduleModel
        let schedules = scheduleEvents.map { ScheduleModel(from: $0) }

        // 按开始时间排序
        let sortedSchedules = schedules.sorted { $0.startDate < $1.startDate }

        // 更新UI
        self.schedules = sortedSchedules
        self.isLoading = false
    }
    
    // MARK: - 判断是否为节日事件
    private func isHolidayEvent(_ event: EKEvent) -> Bool {

        // 系统节日通常来自特定的日历类型
        if let calendar = event.calendar {
            // 检查日历类型来判断是否为节日
            if calendar.type != .calDAV {
                return true
            }

            // 检查日历是否为只读（系统节日日历通常是只读的）
            if !calendar.allowsContentModifications {
                return true
            }
        }

        return false
    }
    
    // MARK: - 获取今日日程
    func getTodaySchedules() -> [ScheduleModel] {
        let today = Date()
        return schedules.filter { schedule in
            Calendar.current.isDate(schedule.startDate, inSameDayAs: today)
        }
    }
    
    // MARK: - 按年份加载日程数据
    func loadSchedules(for year: Int) {
        loadSchedules(year: year)
    }

    // MARK: - 获取即将到来的日程（不包括今天的日程，避免重复）
    func getUpcomingSchedules(days: Int = 7) -> [ScheduleModel] {
        let today = Date()
        let calendar = Calendar.current
        let startOfToday = calendar.startOfDay(for: today)
        let startOfTomorrow = calendar.date(byAdding: .day, value: 1, to: startOfToday) ?? today
        let futureDate = calendar.date(byAdding: .day, value: days, to: startOfToday) ?? today

        return schedules.filter { schedule in
            // 只包括明天及以后的日程，不包括今天的日程
            schedule.startDate >= startOfTomorrow && schedule.startDate <= futureDate
        }.sorted { $0.startDate < $1.startDate }
    }
    
    // MARK: - 搜索日程
    func searchSchedules(query: String) -> [ScheduleModel] {
        guard !query.isEmpty else { return schedules }
        
        return schedules.filter { schedule in
            schedule.title.localizedCaseInsensitiveContains(query) ||
            schedule.location?.localizedCaseInsensitiveContains(query) ?? false ||
            schedule.notes?.localizedCaseInsensitiveContains(query) ?? false
        }
    }
    
    // MARK: - 按日期分组日程
    func groupSchedulesByDate() -> [ScheduleGroup] {
        let groupedDict = Dictionary(grouping: schedules) { schedule in
            Calendar.current.startOfDay(for: schedule.startDate)
        }
        
        return groupedDict.map { date, schedules in
            ScheduleGroup(date: date, schedules: schedules.sorted { $0.startDate < $1.startDate })
        }.sorted { $0.date < $1.date }
    }
    
    // MARK: - 手动请求日历权限
    func requestCalendarPermission() {
        let status = EKEventStore.authorizationStatus(for: .event)
        
        switch status {
        case .notDetermined:
            requestCalendarAccess()
        case .denied, .restricted:
            openSettings()
        case .authorized, .fullAccess:
            calendarAccessGranted = true
            loadSchedules()
        case .writeOnly:
            requestCalendarAccess()
        @unknown default:
            requestCalendarAccess()
        }
    }
    
    // MARK: - 打开设置页面
    private func openSettings() {
        DispatchQueue.main.async {
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
        }
    }
}
