//
//  WidgetURLHandler.swift
//  DianDian
//
//  Created by LeeHom on 2025/8/3.
//

import Foundation
import SwiftUI

// MARK: - Widget URL Handler
class WidgetURLHandler: ObservableObject {
    static let shared = WidgetURLHandler()
    
    @Published var shouldShowAddEvent = false
    @Published var shouldShowEventList = false
    @Published var selectedEventID: String?
    
    private init() {}
    
    // MARK: - URL Handling
    func handleURL(_ url: URL) {
        print("🔗 Widget URL Handler: 处理URL - \(url.absoluteString)")
        
        guard url.scheme == "diandian" else {
            print("❌ Widget URL Handler: 不支持的URL scheme")
            return
        }
        
        let host = url.host
        let pathComponents = url.pathComponents
        
        switch host {
        case "add":
            // 添加新事件
            print("➕ Widget URL Handler: 打开添加事件页面")
            shouldShowAddEvent = true
            
        case "events":
            // 显示事件列表
            print("📋 Widget URL Handler: 打开事件列表")
            shouldShowEventList = true
            
        case "event":
            // 显示特定事件详情
            if pathComponents.count > 1 {
                let eventID = pathComponents[1]
                print("📝 Widget URL Handler: 打开事件详情 - \(eventID)")
                selectedEventID = eventID
            }
            
        default:
            print("❓ Widget URL Handler: 未知的URL host - \(host ?? "nil")")
        }
    }
    
    // MARK: - Reset State
    func resetState() {
        shouldShowAddEvent = false
        shouldShowEventList = false
        selectedEventID = nil
    }
}

// MARK: - App State Manager Extension
extension AppStateManager {
    func handleWidgetURL(_ url: URL) {
        WidgetURLHandler.shared.handleURL(url)
    }
}
