//
//  DataImportExportManager.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import Foundation
import SwiftUI

// MARK: - 数据导入导出管理器
class DataImportExportManager: ObservableObject {
    static let shared = DataImportExportManager()
    
    @Published var isExporting = false
    @Published var isImporting = false
    @Published var exportProgress: Double = 0.0
    @Published var importProgress: Double = 0.0
    @Published var lastExportDate: Date?
    @Published var lastImportDate: Date?
    
    private init() {
        loadLastOperationDates()
    }
    
    // MARK: - 导出数据
    func exportData() async -> URL? {
        await MainActor.run {
            isExporting = true
            exportProgress = 0.0
        }
        
        do {
            // 获取所有数据
            // 注意：此功能已废弃，因为已迁移到SwiftData
            let events: [EventSwiftData] = []
            let archivedEvents: [EventSwiftData] = []
            
            await updateProgress(0.3)
            
            // 创建导出数据结构
            let exportData = ExportData(
                version: "1.0",
                exportDate: Date(),
                events: events.map { ExportEventData(from: $0) },
                archivedEvents: archivedEvents.map { ExportEventData(from: $0) },
                appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
            )
            
            await updateProgress(0.6)
            
            // 编码为 JSON
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = .prettyPrinted
            
            let jsonData = try encoder.encode(exportData)
            
            await updateProgress(0.8)
            
            // 保存到临时文件
            let fileName = "DianDian_Backup_\(formatDateForFileName(Date())).json"
            let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
            
            try jsonData.write(to: tempURL)
            
            await updateProgress(1.0)
            
            // 更新最后导出时间
            await MainActor.run {
                lastExportDate = Date()
                saveLastOperationDates()
                isExporting = false
            }
            
            return tempURL
            
        } catch {
            await MainActor.run {
                isExporting = false
                print("导出失败: \(error)")
            }
            return nil
        }
    }
    
    // MARK: - 导入数据
    func importData(from url: URL) async -> ImportResult {
        await MainActor.run {
            isImporting = true
            importProgress = 0.0
        }
        
        do {
            // 读取文件
            let jsonData = try Data(contentsOf: url)
            
            await updateImportProgress(0.2)
            
            // 解码 JSON
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            let importData = try decoder.decode(ExportData.self, from: jsonData)
            
            await updateImportProgress(0.5)
            
            // 验证数据版本
            guard isCompatibleVersion(importData.version) else {
                await MainActor.run { isImporting = false }
                return .failure("不兼容的数据版本: \(importData.version)")
            }
            
            // 注意：此功能已废弃，因为已迁移到SwiftData
            await updateImportProgress(1.0)

            // 更新最后导入时间
            await MainActor.run {
                lastImportDate = Date()
                saveLastOperationDates()
                isImporting = false
            }

            return .failure("导入功能已废弃，请使用SwiftData版本")
            
        } catch {
            await MainActor.run {
                isImporting = false
                print("导入失败: \(error)")
            }
            return .failure("导入失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - 生成示例数据
    func generateSampleData() -> URL? {
        let sampleEvents = [
            createSampleEvent(title: "妈妈的生日", date: "2024-12-25", icon: "birthday", color: "FF6B6B", note: "记得准备生日礼物和生日蛋糕"),
            createSampleEvent(title: "结婚纪念日", date: "2024-08-15", icon: "wedding", color: "4ECDC4", note: "我们的特殊日子"),
            createSampleEvent(title: "毕业典礼", date: "2024-06-30", icon: "graduation", color: "45B7D1", note: "人生新的开始"),
            createSampleEvent(title: "第一次约会", date: "2024-02-14", icon: "heart", color: "DDA0DD", note: "美好回忆的开始"),
            createSampleEvent(title: "宝宝出生", date: "2024-09-10", icon: "baby", color: "96CEB4", note: "小天使降临的日子"),
            createSampleEvent(title: "搬新家", date: "2024-05-20", icon: "home", color: "FFEAA7", note: "新家新开始"),
            createSampleEvent(title: "工作入职", date: "2024-03-01", icon: "work", color: "98D8C8", note: "职业生涯的新篇章")
        ]

        let exportData = ExportData(
            version: "1.0",
            exportDate: Date(),
            events: sampleEvents.map { ExportEventData(from: $0) },
            archivedEvents: [],
            appVersion: "1.0.0"
        )

        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = .prettyPrinted

            let jsonData = try encoder.encode(exportData)

            let fileName = "DianDian_Sample_Data.json"
            let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)

            try jsonData.write(to: tempURL)
            return tempURL

        } catch {
            print("生成示例数据失败: \(error)")
            return nil
        }
    }

    // MARK: - 数据验证
    func validateExportData(_ data: ExportData) -> ValidationResult {
        var issues: [String] = []

        // 检查版本兼容性
        if !isCompatibleVersion(data.version) {
            issues.append("不兼容的数据版本: \(data.version)")
        }

        // 检查事件数据
        for (index, event) in data.events.enumerated() {
            if event.title.isEmpty {
                issues.append("事件 \(index + 1) 缺少标题")
            }
            if event.date.isEmpty {
                issues.append("事件 \(index + 1) 缺少日期")
            }
            if !isValidDateString(event.date) {
                issues.append("事件 \(index + 1) 日期格式无效: \(event.date)")
            }
        }

        // 检查归档事件数据
        for (index, event) in data.archivedEvents.enumerated() {
            if event.title.isEmpty {
                issues.append("归档事件 \(index + 1) 缺少标题")
            }
            if event.date.isEmpty {
                issues.append("归档事件 \(index + 1) 缺少日期")
            }
        }

        return ValidationResult(isValid: issues.isEmpty, issues: issues)
    }

    // MARK: - 数据统计
    func getDataStatistics() -> DataStatistics {
        // 注意：此功能已废弃，因为已迁移到SwiftData
        let totalEvents = 0
        let activeEvents = 0
        let archivedCount = 0

        let eventsWithNotifications = 0
        let lunarEvents = 0

        let oldestEvent: EventSwiftData? = nil
        let newestEvent: EventSwiftData? = nil

        return DataStatistics(
            totalEvents: totalEvents,
            activeEvents: activeEvents,
            archivedEvents: archivedCount,
            eventsWithNotifications: eventsWithNotifications,
            lunarEvents: lunarEvents,
            oldestEventDate: oldestEvent?.createdAt,
            newestEventDate: newestEvent?.createdAt
        )
    }
    
    // MARK: - 私有方法
    
    @MainActor
    private func updateProgress(_ progress: Double) {
        exportProgress = progress
    }
    
    @MainActor
    private func updateImportProgress(_ progress: Double) {
        importProgress = progress
    }
    
    // 此方法已废弃，因为已迁移到SwiftData
    private func createSampleEvent(title: String, date: String, icon: String, color: String, note: String = "") -> EventSwiftData {
        let event = EventSwiftData()
        event.id = UUID().uuidString
        event.title = title
        event.date = date
        event.iconName = icon
        event.color = color
        event.note = note
        event.isNotificationEnabled = true
        event.notificationTime = "09:00"
        event.cycleType = EventCycleType.yearly.rawValue
        event.createdAt = Date()
        event.updatedAt = Date()
        return event
    }

    private func isValidDateString(_ dateString: String) -> Bool {
        let formatter = createISODateFormatter()
        return formatter.date(from: dateString) != nil
    }

    private func createISODateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone.current
        return formatter
    }
    
    private func isCompatibleVersion(_ version: String) -> Bool {
        // 简单的版本兼容性检查
        return version.hasPrefix("1.")
    }
    
    private func formatDateForFileName(_ date: Date) -> String {
        let formatter = createFileNameDateFormatter()
        return formatter.string(from: date)
    }

    private func createFileNameDateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone.current
        return formatter
    }
    
    private func loadLastOperationDates() {
        let userDefaults = UserDefaults.standard
        if let exportDate = userDefaults.object(forKey: "LastExportDate") as? Date {
            lastExportDate = exportDate
        }
        if let importDate = userDefaults.object(forKey: "LastImportDate") as? Date {
            lastImportDate = importDate
        }
    }
    
    private func saveLastOperationDates() {
        let userDefaults = UserDefaults.standard
        if let exportDate = lastExportDate {
            userDefaults.set(exportDate, forKey: "LastExportDate")
        }
        if let importDate = lastImportDate {
            userDefaults.set(importDate, forKey: "LastImportDate")
        }
    }
}

// MARK: - 导出数据结构
struct ExportData: Codable {
    let version: String
    let exportDate: Date
    let events: [ExportEventData]
    let archivedEvents: [ExportEventData]
    let appVersion: String
}

struct ExportEventData: Codable {
    let id: String
    let title: String
    let date: String
    let notificationTime: String
    let cycleType: Int
    let color: String
    let iconName: String
    let backgroundImageName: String
    let isLunar: Bool
    let isArchived: Bool
    let advanceNotificationType: Int
    let note: String
    let isNotificationEnabled: Bool
    let createdAt: Date
    let updatedAt: Date
    
    init(from event: EventSwiftData) {
        self.id = event.id
        self.title = event.title
        self.date = event.date
        self.notificationTime = event.notificationTime
        self.cycleType = event.cycleType
        self.color = event.color
        self.iconName = event.iconName
        self.backgroundImageName = event.backgroundImageName
        self.isLunar = event.isLunar
        self.isArchived = event.isArchived
        self.advanceNotificationType = event.advanceNotificationType
        self.note = event.note
        self.isNotificationEnabled = event.isNotificationEnabled
        self.createdAt = event.createdAt
        self.updatedAt = event.updatedAt
    }
    
    func toEventSwiftData() -> EventSwiftData {
        let event = EventSwiftData()
        event.id = self.id
        event.title = self.title
        event.date = self.date
        event.notificationTime = self.notificationTime
        event.cycleType = self.cycleType
        event.color = self.color
        event.iconName = self.iconName
        event.backgroundImageName = self.backgroundImageName
        event.isLunar = self.isLunar
        event.isArchived = self.isArchived
        event.advanceNotificationType = self.advanceNotificationType
        event.note = self.note
        event.isNotificationEnabled = self.isNotificationEnabled
        event.createdAt = self.createdAt
        event.updatedAt = self.updatedAt
        return event
    }
}

// MARK: - 导入结果
enum ImportResult {
    case success(Int) // 导入的事件数量
    case failure(String) // 错误信息
}

// MARK: - 数据验证结果
struct ValidationResult {
    let isValid: Bool
    let issues: [String]
}

// MARK: - 数据统计信息
struct DataStatistics {
    let totalEvents: Int
    let activeEvents: Int
    let archivedEvents: Int
    let eventsWithNotifications: Int
    let lunarEvents: Int
    let oldestEventDate: Date?
    let newestEventDate: Date?

    var formattedOldestDate: String {
        guard let date = oldestEventDate else { return NSLocalizedString("无", comment: "None") }
        let formatter = createYearMonthDayFormatter()
        return formatter.string(from: date)
    }

    var formattedNewestDate: String {
        guard let date = newestEventDate else { return NSLocalizedString("无", comment: "None") }
        let formatter = createYearMonthDayFormatter()
        return formatter.string(from: date)
    }

    private func createYearMonthDayFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        formatter.locale = Locale.current
        return formatter
    }
}
