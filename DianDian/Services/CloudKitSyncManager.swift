//
//  CloudKitSyncManager.swift
//  DianDian
//
//  Created by LeeHom on 2025/8/2.
//

import Foundation
import SwiftUI
import SwiftData
import CloudKit
import Combine

// MARK: - 同步状态枚举
enum CloudKitSyncStatus: Equatable {
    case idle           // 空闲状态
    case syncing        // 正在同步
    case success        // 同步成功
    case failed(Error)  // 同步失败
    case disabled       // 同步已禁用

    static func == (lhs: CloudKitSyncStatus, rhs: CloudKitSyncStatus) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.syncing, .syncing), (.success, .success), (.disabled, .disabled):
            return true
        case (.failed(let lhsError), .failed(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
}

// MARK: - iCloud账户状态
enum iCloudAccountStatus {
    case available      // 可用
    case noAccount      // 未登录iCloud
    case restricted     // 受限制
    case couldNotDetermine // 无法确定
}

// MARK: - CloudKit同步管理器
class CloudKitSyncManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var syncStatus: CloudKitSyncStatus = .idle
    @Published var accountStatus: iCloudAccountStatus = .couldNotDetermine
    @Published var lastSyncDate: Date?
    @Published var isEnabled: Bool = true
    
    // MARK: - Private Properties
    private let container = CKContainer(identifier: "iCloud.com.iamlihongking.dotdot")
    private var cancellables = Set<AnyCancellable>()
    private let userDefaults = UserDefaults.standard
    
    // MARK: - Constants
    private struct UserDefaultsKeys {
        static let lastSyncDate = "CloudKitLastSyncDate"
        static let syncEnabled = "EnableiCloudSync"  // 与AppStateManager保持一致
    }
    
    // MARK: - Initialization
    init() {
        loadSettings()
        setupObservers()

        // 初始化时检查账户状态
        Task {
            await checkAccountStatus()
        }
    }
    
    // MARK: - Public Methods
    
    /// 检查iCloud账户状态
    func checkAccountStatus() async {
        print("🔍 开始检查iCloud账户状态...")

        #if targetEnvironment(simulator)
        // 模拟器环境下的特殊处理
        await MainActor.run {
            self.accountStatus = .couldNotDetermine
            print("📱 模拟器环境：CloudKit功能受限")
        }
        return
        #endif

        do {
            let status = try await container.accountStatus()
            await MainActor.run {
                print("📱 CloudKit返回状态: \(status)")
                switch status {
                case .available:
                    self.accountStatus = .available
                    print("✅ iCloud账户可用")
                case .noAccount:
                    self.accountStatus = .noAccount
                    print("⚠️ 未登录iCloud账户")
                case .restricted:
                    self.accountStatus = .restricted
                    print("🚫 iCloud账户受限")
                case .couldNotDetermine:
                    self.accountStatus = .couldNotDetermine
                    print("❓ 无法确定iCloud账户状态")
                case .temporarilyUnavailable:
                    self.accountStatus = .couldNotDetermine
                    print("⏳ iCloud服务临时不可用")
                @unknown default:
                    self.accountStatus = .couldNotDetermine
                    print("❓ 未知的iCloud账户状态")
                }
                print("📱 最终账户状态: \(self.accountStatus)")
            }
        } catch {
            await MainActor.run {
                self.accountStatus = .couldNotDetermine
                print("❌ 检查iCloud账户状态失败: \(error)")
                print("❌ 错误详情: \(error.localizedDescription)")
            }
        }
    }
    
    /// 启用或禁用同步（仅更新状态，不自动执行同步）
    func setSyncEnabled(_ enabled: Bool) {
        // 避免重复设置相同的值
        guard isEnabled != enabled else { return }

        isEnabled = enabled
        userDefaults.set(enabled, forKey: UserDefaultsKeys.syncEnabled)

        print("🔄 CloudKit同步已\(enabled ? "启用" : "禁用")")

        // 移除自动同步逻辑，只更新状态
        if enabled {
            Task {
                await checkAccountStatus()
            }
        } else {
            syncStatus = .disabled
        }
    }
    
    /// 手动触发同步（不依赖isEnabled状态）
    func manualSync() async {
        await checkAccountStatus()
        guard accountStatus == .available else {
            syncStatus = .failed(CloudKitError.accountNotAvailable)
            return
        }

        await performSync()
    }
    
    /// 获取同步状态描述
    var syncStatusDescription: String {
        switch syncStatus {
        case .idle:
            return "空闲"
        case .syncing:
            return "正在同步..."
        case .success:
            if let lastSync = lastSyncDate {
                let formatter = DateFormatter()
                formatter.dateStyle = .short
                formatter.timeStyle = .short
                return "上次同步: \(formatter.string(from: lastSync))"
            } else {
                return "同步成功"
            }
        case .failed(let error):
            return "同步失败: \(error.localizedDescription)"
        case .disabled:
            return "同步已禁用"
        }
    }
    
    /// 获取账户状态描述
    var accountStatusDescription: String {
        switch accountStatus {
        case .available:
            return "iCloud可用"
        case .noAccount:
            return "未登录iCloud"
        case .restricted:
            return "iCloud受限"
        case .couldNotDetermine:
            #if targetEnvironment(simulator)
            return "模拟器环境"
            #else
            return "检查中..."
            #endif
        }
    }
    
    // MARK: - Private Methods
    
    private func loadSettings() {
        isEnabled = userDefaults.bool(forKey: UserDefaultsKeys.syncEnabled)
        if let lastSyncData = userDefaults.object(forKey: UserDefaultsKeys.lastSyncDate) as? Date {
            lastSyncDate = lastSyncData
        }
    }
    
    private func setupObservers() {
        // 这里不需要监听isEnabled的变化，避免无限循环
        // 状态变化由外部的AppStateManager管理
    }
    
    private func performSync() async {
        await MainActor.run {
            self.syncStatus = .syncing
        }

        do {
            // SwiftData + CloudKit会自动处理同步
            // 我们通过检查CloudKit状态来验证同步
            let database = container.privateCloudDatabase

            // 尝试执行一个简单的CloudKit操作来触发同步
            _ = try await database.allRecordZones()

            // 给SwiftData一些时间来处理同步
            try await Task.sleep(nanoseconds: 2_000_000_000) // 2秒

            await MainActor.run {
                self.syncStatus = .success
                self.lastSyncDate = Date()
                self.userDefaults.set(self.lastSyncDate, forKey: UserDefaultsKeys.lastSyncDate)
                print("✅ CloudKit同步完成")
            }
        } catch {
            await MainActor.run {
                self.syncStatus = .failed(CloudKitError.unknown(error))
                print("❌ CloudKit同步失败: \(error)")
            }
        }
    }
}

// MARK: - CloudKit错误类型
enum CloudKitError: LocalizedError {
    case accountNotAvailable
    case networkUnavailable
    case quotaExceeded
    case unknown(Error)
    
    var errorDescription: String? {
        switch self {
        case .accountNotAvailable:
            return "iCloud账户不可用"
        case .networkUnavailable:
            return "网络不可用"
        case .quotaExceeded:
            return "iCloud存储空间不足"
        case .unknown(let error):
            return error.localizedDescription
        }
    }
}
