//
//  WidgetDataProvider.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import Foundation
import SwiftData

// MARK: - Widget Data Provider
class WidgetDataProvider {
    static let shared = WidgetDataProvider()

    // App Group标识符
    private let appGroupIdentifier = "group.com.iamlihongking.dotdot"

    private init() {}

    // MARK: - Model Container
    private lazy var modelContainer: ModelContainer = {
        do {
            // 获取App Group共享目录
            guard let appGroupURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) else {
                print("❌ Widget: 无法获取App Group目录")
                // 回退到默认容器
                return try ModelContainer(for: EventSwiftData.self)
            }

            // 创建共享数据库路径
            let storeURL = appGroupURL.appendingPathComponent("DianDian.sqlite")
            print("✅ Widget: 使用共享数据库路径: \(storeURL.path)")

            // 配置ModelContainer使用共享存储
            let configuration = ModelConfiguration(url: storeURL)
            let container = try ModelContainer(for: EventSwiftData.self, configurations: configuration)

            print("✅ Widget: ModelContainer创建成功")
            return container
        } catch {
            print("❌ Widget: 创建ModelContainer失败: \(error)")
            // 创建默认容器作为回退
            do {
                return try ModelContainer(for: EventSwiftData.self)
            } catch {
                fatalError("Widget: 无法创建任何ModelContainer")
            }
        }
    }()

    // MARK: - Data Access
    func getActiveEvents() -> [EventSwiftData] {
        do {
            let context = ModelContext(modelContainer)

            // 查询活跃事件
            let descriptor = FetchDescriptor<EventSwiftData>(
                predicate: #Predicate { event in
                    !event.isArchived
                },
                sortBy: [SortDescriptor(\.date)]
            )

            let events = try context.fetch(descriptor)
            print("✅ Widget: 成功加载 \(events.count) 个活跃事件")

            return events
        } catch {
            print("❌ Widget: 加载事件数据失败: \(error)")
            return []
        }
    }

    func getUpcomingEvents(limit: Int = 10) -> [EventSwiftData] {
        let allEvents = getActiveEvents()

        // 过滤并排序即将到来的事件
        let upcomingEvents = allEvents
            .filter { $0.daysUntilNext >= 0 }
            .sorted { $0.daysUntilNext < $1.daysUntilNext }
            .prefix(limit)
            .map { $0 }

        print("✅ Widget: 筛选出 \(upcomingEvents.count) 个即将到来的事件")
        return upcomingEvents
    }

    // MARK: - Convert to Widget Data
    func convertToWidgetData(_ events: [EventSwiftData]) -> [WidgetEventData] {
        return events.map { event in
            WidgetEventData(from: event)
        }
    }

    // MARK: - Sample Data
    func getSampleEvents() -> [WidgetEventData] {
        return [
            WidgetEventData(
                id: "sample1",
                title: "Sample1",
                date: "2025-08-10",
                iconName: "gift.fill",
                color: "pink",
                daysUntilNext: 7,
                isLunar: true
            ),
            WidgetEventData(
                id: "sample2",
                title: "Sample2",
                date: "2025-01-29",
                iconName: "sparkles",
                color: "red",
                daysUntilNext: 15,
                isLunar: true
            ),
            WidgetEventData(
                id: "sample3",
                title: "Sample3",
                date: "2025-09-01",
                iconName: "heart.fill",
                color: "purple",
                daysUntilNext: 30,
                isLunar: false
            )
        ]
    }
}



// MARK: - UserDefaults Extension for App Group
extension UserDefaults {
    static let appGroup = UserDefaults(suiteName: "group.com.iamlihongking.dotdot") ?? UserDefaults.standard
}
