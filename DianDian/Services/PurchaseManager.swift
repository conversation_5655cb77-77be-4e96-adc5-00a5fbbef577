//
//  PurchaseManager.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import Foundation
import StoreKit
import SwiftUI

// MARK: - 应用内购买管理器
@MainActor
class PurchaseManager: ObservableObject {
    static let shared = PurchaseManager()
    
    @Published var products: [Product] = []
    @Published var purchasedProductIDs: Set<String> = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // 非消耗型高级版产品ID
    private let productIDs = [
        "diandian_premium"
    ]
    
    private var updateListenerTask: Task<Void, Error>?
    
    private init() {
        updateListenerTask = listenForTransactions()
        
        Task {
            await requestProducts()
            await updateCustomerProductStatus()
        }
    }
    
    deinit {
        updateListenerTask?.cancel()
    }
    
    // MARK: - 产品请求
    func requestProducts() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let storeProducts = try await Product.products(for: productIDs)
            products = storeProducts.sorted { $0.price < $1.price }
        } catch {
            errorMessage = "无法加载产品信息: \(error.localizedDescription)"
            print("Failed to request products: \(error)")
        }
        
        isLoading = false
    }
    
    // MARK: - 购买产品
    func purchase(_ product: Product) async throws -> StoreKit.Transaction? {
        let result = try await product.purchase()
        
        switch result {
        case .success(let verification):
            let transaction = try await checkVerified(verification)
            
            // 更新购买状态
            await updateCustomerProductStatus()
            
            // 完成交易
            await transaction.finish()
            
            return transaction
            
        case .userCancelled, .pending:
            return nil
            
        default:
            return nil
        }
    }
    
    // MARK: - 恢复购买
    func restorePurchases() async {
        do {
            try await AppStore.sync()
            await updateCustomerProductStatus()
        } catch {
            errorMessage = "恢复购买失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 更新购买状态
    func updateCustomerProductStatus() async {
        var purchasedProducts: Set<String> = []

        for await result in StoreKit.Transaction.currentEntitlements {
            do {
                let transaction = try await checkVerified(result)

                // 对于非消耗型产品，只要交易存在就表示已购买
                if transaction.productType == .nonConsumable {
                    purchasedProducts.insert(transaction.productID)
                }
            } catch {
                print("Failed to verify transaction: \(error)")
            }
        }

        purchasedProductIDs = purchasedProducts
        print("✅ 购买状态已更新: \(purchasedProducts)")
    }
    
    // MARK: - 监听交易
    private func listenForTransactions() -> Task<Void, Error> {
        return Task.detached {
            for await result in StoreKit.Transaction.updates {
                do {
                    let transaction = try await self.checkVerified(result)
                    
                    await self.updateCustomerProductStatus()
                    
                    await transaction.finish()
                } catch {
                    print("Transaction failed verification: \(error)")
                }
            }
        }
    }
    
    // MARK: - 验证交易
    private func checkVerified<T>(_ result: VerificationResult<T>) async throws -> T {
        switch result {
        case .unverified:
            throw StoreError.failedVerification
        case .verified(let safe):
            return safe
        }
    }
    
    // MARK: - 便利方法
    
    /// 检查是否为高级版用户
    var isPremiumUser: Bool {
        purchasedProductIDs.contains("diandian_premium")
    }

    /// 获取高级版产品
    var premiumProduct: Product? {
        products.first { $0.id == "diandian_premium" }
    }

    /// 获取高级版产品价格
    var premiumPrice: String {
        guard let product = premiumProduct else {
            // 根据系统语言返回相应的加载文本
            if Locale.current.language.languageCode?.identifier == "zh" {
                return "获取价格中..."
            } else {
                return "Loading price..."
            }
        }
        return formatPriceForCurrentLanguage(product: product)
    }

    /// 根据当前语言格式化价格显示
    private func formatPriceForCurrentLanguage(product: Product) -> String {
        // 如果是中文环境，尝试本地化价格显示
        if Locale.current.language.languageCode?.identifier == "zh" {
            // 提取数字价格
            let priceValue = product.price

            // 创建中文数字格式化器
            let formatter = NumberFormatter()
            formatter.numberStyle = .currency
            formatter.locale = Locale(identifier: "zh_CN")

            // 如果能格式化成功，返回中文格式；否则返回原始格式
            if let formattedPrice = formatter.string(from: priceValue as NSDecimalNumber) {
                return formattedPrice
            }
        }

        // 默认返回原始的displayPrice
        return product.displayPrice
    }
    
    /// 格式化价格
    func formattedPrice(for product: Product) -> String {
        return product.displayPrice
    }
    
    /// 获取购买状态信息
    var purchaseStatus: PurchaseStatus {
        if isPremiumUser {
            return .purchased
        } else {
            return .notPurchased
        }
    }

    /// 检查特定功能是否可用
    func isFeatureAvailable(_ feature: PremiumFeature) -> Bool {
        switch feature {
        case .unlimitedEvents, .advancedNotifications, .iCloudSync, .widgetSupport, .dataExport, .prioritySupport:
            return isPremiumUser
        case .basicFeatures:
            return true
        }
    }

    /// 获取功能限制信息
    func getFeatureLimit(_ feature: PremiumFeature) -> Int? {
        if isPremiumUser {
            return nil // 无限制
        }

        switch feature {
        case .unlimitedEvents:
            return 10 // 免费版最多10个事件
        default:
            return nil
        }
    }
}

// MARK: - Store 错误
enum StoreError: Error {
    case failedVerification
}

// MARK: - 产品扩展
extension Product {
    var localizedPrice: String {
        return displayPrice
    }
}

// MARK: - 购买状态
enum PurchaseStatus {
    case purchased
    case notPurchased

    var displayName: String {
        switch self {
        case .purchased: return "已购买"
        case .notPurchased: return "未购买"
        }
    }
}

// MARK: - 高级功能
enum PremiumFeature: String, CaseIterable {
    case basicFeatures = "basic_features"
    case unlimitedEvents = "unlimited_events"
    case advancedNotifications = "advanced_notifications"
    case iCloudSync = "icloud_sync"
    case widgetSupport = "widget_support"
    case dataExport = "data_export"
    case prioritySupport = "priority_support"

    var displayName: String {
        switch self {
        case .basicFeatures: return "基础功能"
        case .unlimitedEvents: return "无限纪念日"
        case .advancedNotifications: return "高级提醒"
        case .iCloudSync: return "iCloud 同步"
        case .widgetSupport: return "Widget 支持"
        case .dataExport: return "数据导出"
        case .prioritySupport: return "优先客服"
        }
    }

    var description: String {
        switch self {
        case .basicFeatures: return "添加纪念日、基础提醒、颜色主题"
        case .unlimitedEvents: return "添加无限数量的纪念日"
        case .advancedNotifications: return "自定义提醒时间、多次提醒"
        case .iCloudSync: return "在所有设备间同步数据"
        case .widgetSupport: return "桌面小组件显示"
        case .dataExport: return "导出备份数据"
        case .prioritySupport: return "优先技术支持"
        }
    }
}

// MARK: - 模拟购买管理器（用于开发测试）
class MockPurchaseManager: ObservableObject {
    @Published var isPremiumUser = false
    @Published var isLoading = false

    func purchase(productID: String) async -> Bool {
        isLoading = true

        // 模拟网络延迟
        try? await Task.sleep(nanoseconds: 2_000_000_000)

        isPremiumUser = true
        isLoading = false

        return true
    }

    func restorePurchases() async -> Bool {
        isLoading = true

        // 模拟网络延迟
        try? await Task.sleep(nanoseconds: 1_000_000_000)

        // 模拟恢复结果（50% 概率有购买记录）
        isPremiumUser = Bool.random()
        isLoading = false

        return isPremiumUser
    }

    func isFeatureAvailable(_ feature: PremiumFeature) -> Bool {
        switch feature {
        case .basicFeatures:
            return true
        default:
            return isPremiumUser
        }
    }
}
