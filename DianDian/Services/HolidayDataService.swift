//
//  HolidayDataService.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import Foundation
import Combine
import EventKit
import SwiftUI

// MARK: - 节日类型枚举
enum HolidayType: String, Codable, CaseIterable {
    case traditional = "traditional"
    case western = "western"
    case official = "official"
    case international = "international"
    case calendar = "calendar"

    var displayName: String {
        switch self {
        case .traditional: return "传统节日"
        case .western: return "西方节日"
        case .official: return "法定节假日"
        case .international: return "国际节日"
        case .calendar: return "日历节日"
        }
    }

    var color: Color {
        switch self {
        case .traditional: return .red
        case .western: return .blue
        case .official: return .green
        case .international: return .orange
        case .calendar: return .purple
        }
    }

    var icon: String {
        switch self {
        case .traditional: return "moon.stars"
        case .western: return "star"
        case .official: return "flag"
        case .international: return "globe"
        case .calendar: return "calendar"
        }
    }
}

// MARK: - 节日数据模型
struct HolidayModel: Codable, Identifiable {
    let id: String
    let name: String
    let startDate: String
    let endDate: String
    let type: HolidayType
    let description: String
    let isOfficial: Bool
    let calendarColor: String? // 从EKCalendar获取的颜色
    let calendarTitle: String? // 从EKCalendar获取的标题
    let isBirthday: Bool // 是否为生日事件

    var startDateObject: Date? {
        let formatter = createISODateFormatter()
        return formatter.date(from: startDate)
    }

    private func createISODateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone.current
        return formatter
    }

    var daysUntil: Int {
        guard let startDate = startDateObject else { return -1 }

        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let targetDate = calendar.startOfDay(for: startDate)

        let components = calendar.dateComponents([.day], from: today, to: targetDate)
        return components.day ?? -1
    }

    // 获取主题色，优先使用日历颜色，否则使用类型默认颜色
    var themeColor: Color {
        if let colorString = calendarColor, let color = Color(hex: colorString) {
            return color
        }
        return type.color
    }
}

// MARK: - 节日数据服务
class HolidayDataService: ObservableObject {
    @Published var holidays: [HolidayModel] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var calendarAccessGranted: Bool = false

    private var cancellables = Set<AnyCancellable>()
    private let eventStore = EKEventStore()

    init() {
        checkCalendarAccess()
    }

    // MARK: - 检查日历权限
    private func checkCalendarAccess() {
        switch EKEventStore.authorizationStatus(for: .event) {
        case .authorized:
            calendarAccessGranted = true
        case .fullAccess:
            calendarAccessGranted = true
        case .writeOnly:
            calendarAccessGranted = false
            errorMessage = "需要完整日历权限来读取节日信息"
        case .notDetermined:
            requestCalendarAccess()
        case .denied, .restricted:
            calendarAccessGranted = false
            errorMessage = "需要日历权限来读取节日信息"
        @unknown default:
            calendarAccessGranted = false
        }
    }

    // MARK: - 请求日历权限
    private func requestCalendarAccess() {
        if #available(iOS 17.0, *) {
            eventStore.requestFullAccessToEvents { [weak self] granted, error in
                DispatchQueue.main.async {
                    self?.calendarAccessGranted = granted
                    if !granted {
                        self?.errorMessage = "需要日历权限来显示节日信息"
                        self?.holidays = []
                    } else {
                        self?.errorMessage = nil
                        // 权限获得后重新加载数据
                        let currentYear = Calendar.current.component(.year, from: Date())
                        self?.loadHolidays(for: currentYear)
                    }
                }
            }
        } else {
            eventStore.requestAccess(to: .event) { [weak self] granted, error in
                DispatchQueue.main.async {
                    self?.calendarAccessGranted = granted
                    if !granted {
                        self?.errorMessage = "需要日历权限来显示节日信息"
                        self?.holidays = []
                    } else {
                        self?.errorMessage = nil
                        // 权限获得后重新加载数据
                        let currentYear = Calendar.current.component(.year, from: Date())
                        self?.loadHolidays(for: currentYear)
                    }
                }
            }
        }
    }

    // MARK: - 加载节日数据
    func loadHolidays(for year: Int = Calendar.current.component(.year, from: Date())) {
        isLoading = true
        errorMessage = nil

        if calendarAccessGranted {
            loadCalendarHolidays(for: year)
        } else {
            // 如果没有日历权限，清空数据并显示权限提示
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.holidays = []
                self.isLoading = false
                if !self.calendarAccessGranted {
                    self.errorMessage = "需要日历权限来显示节日信息"
                }
            }
        }
    }
    
    // MARK: - 从日历读取节日数据
    private func loadCalendarHolidays(for year: Int) {
        let calendar = Calendar.current

        // 只加载选定年份的数据
        guard let startDate = calendar.date(from: DateComponents(year: year, month: 1, day: 1)),
              let endDate = calendar.date(from: DateComponents(year: year + 1, month: 1, day: 1)) else {
            DispatchQueue.main.async {
                self.holidays = []
                self.isLoading = false
                self.errorMessage = "无法创建日期范围"
            }
            return
        }

        // 获取所有日历
        let calendars = eventStore.calendars(for: .event)

        // 创建谓词来查找节日事件
        let predicate = eventStore.predicateForEvents(withStart: startDate, end: endDate, calendars: calendars)

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            let events = self?.eventStore.events(matching: predicate) ?? []

            // 只获取节日事件，过滤掉日程
            let holidayEvents = events.filter { event in
                self?.isHolidayEvent(event) ?? false
            }

            // 转换为HolidayModel
            var holidays: [HolidayModel] = []
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"

            // 为每个事件创建单独的节日模型
            for event in holidayEvents {
                let dateString = formatter.string(from: event.startDate)

                // 获取日历颜色和标题
                var calendarColor: String? = nil
                var calendarTitle: String? = nil

                if let calendar = event.calendar {
                    // 获取日历颜色
                    if let cgColor = calendar.cgColor {
                        let color = UIColor(cgColor: cgColor)
                        var red: CGFloat = 0
                        var green: CGFloat = 0
                        var blue: CGFloat = 0
                        var alpha: CGFloat = 0
                        color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

                        calendarColor = String(format: "#%02X%02X%02X",
                                             Int(red * 255),
                                             Int(green * 255),
                                             Int(blue * 255))
                    }

                    // 获取日历标题
                    calendarTitle = calendar.title
                }

                // 检查是否为生日事件
                let isBirthday = self?.isBirthdayEvent(event) ?? false

                let holiday = HolidayModel(
                    id: UUID().uuidString,
                    name: event.title ?? "未知事件",
                    startDate: dateString,
                    endDate: dateString,
                    type: .calendar,
                    description: event.notes ?? "",
                    isOfficial: false,
                    calendarColor: calendarColor,
                    calendarTitle: calendarTitle,
                    isBirthday: isBirthday
                )
                holidays.append(holiday)
            }

            // 去重并排序
            let uniqueHolidays = self?.removeDuplicateHolidays(holidays) ?? holidays
            let sortedHolidays = uniqueHolidays.sorted { holiday1, holiday2 in
                guard let date1 = holiday1.startDateObject,
                      let date2 = holiday2.startDateObject else { return false }
                return date1 < date2
            }

            DispatchQueue.main.async {
                self?.holidays = sortedHolidays
                self?.isLoading = false
            }
        }
    }



    // MARK: - 去除重复节日
    private func removeDuplicateHolidays(_ holidays: [HolidayModel]) -> [HolidayModel] {
        var uniqueHolidays: [HolidayModel] = []
        var seenDates: Set<String> = []

        for holiday in holidays {
            let key = "\(holiday.startDate)-\(holiday.name)"
            if !seenDates.contains(key) {
                seenDates.insert(key)
                uniqueHolidays.append(holiday)
            }
        }

        return uniqueHolidays
    }


    
    // MARK: - 获取今天的节日
    func getTodayHolidays() -> [HolidayModel] {
        let today = Date()
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        let todayString = formatter.string(from: today)
        
        return holidays.filter { $0.startDate == todayString }
    }
    
    // MARK: - 获取即将到来的节日（未来7天）
    func getUpcomingHolidays(days: Int = 7) -> [HolidayModel] {
        let today = Date()
        let calendar = Calendar.current
        let startOfToday = calendar.startOfDay(for: today)
        let futureDate = calendar.date(byAdding: .day, value: days, to: startOfToday) ?? today

        return holidays.filter { holiday in
            guard let holidayDate = holiday.startDateObject else { return false }
            let startOfHolidayDate = calendar.startOfDay(for: holidayDate)
            return startOfHolidayDate > startOfToday && startOfHolidayDate <= futureDate
        }.sorted { holiday1, holiday2 in
            guard let date1 = holiday1.startDateObject,
                  let date2 = holiday2.startDateObject else { return false }
            return date1 < date2
        }
    }
    
    // MARK: - 搜索节日
    func searchHolidays(query: String) -> [HolidayModel] {
        guard !query.isEmpty else { return holidays }
        
        return holidays.filter { holiday in
            holiday.name.localizedCaseInsensitiveContains(query) ||
            holiday.description.localizedCaseInsensitiveContains(query)
        }
    }

    // MARK: - 手动请求日历权限
    func requestCalendarPermission() {
        let status = EKEventStore.authorizationStatus(for: .event)

        switch status {
        case .notDetermined:
            // 首次请求权限
            requestCalendarAccess()
        case .denied, .restricted:
            // 权限被拒绝，引导用户到设置
            openSettings()
        case .authorized, .fullAccess:
            // 已有权限，更新状态
            calendarAccessGranted = true
            let currentYear = Calendar.current.component(.year, from: Date())
            loadHolidays(for: currentYear)
        case .writeOnly:
            // 只有写权限，需要完整权限
            requestCalendarAccess()
        @unknown default:
            requestCalendarAccess()
        }
    }

    // MARK: - 判断是否为节日事件
    private func isHolidayEvent(_ event: EKEvent) -> Bool {
        // print("节日事件: \(event)")

        // 系统节日通常来自特定的日历类型
        if let calendar = event.calendar {
            // 检查日历类型来判断是否为节日
            if calendar.type != .calDAV {
                return true
            }

            // 检查日历是否为只读（系统节日日历通常是只读的）
            if !calendar.allowsContentModifications {
                return true
            }
        }

        return false
    }

    // MARK: - 判断是否为生日事件
    private func isBirthdayEvent(_ event: EKEvent) -> Bool {
        guard let calendar = event.calendar else { return false }

        // 检查日历类型是否为生日日历
        // 在iOS中，生日日历的类型为EKCalendarTypeBirthday
        if calendar.type == .birthday {
            return true
        }

        // 检查日历标题是否包含生日相关关键词
        let birthdayKeywords = ["生日", "Birthday", "Birthdays", "birthday", "birthdays"]
        let calendarTitle = calendar.title.lowercased()

        for keyword in birthdayKeywords {
            if calendarTitle.contains(keyword.lowercased()) {
                return true
            }
        }

        // 检查事件标题是否包含生日相关关键词
        if let eventTitle = event.title {
            let eventTitleLower = eventTitle.lowercased()
            for keyword in birthdayKeywords {
                if eventTitleLower.contains(keyword.lowercased()) {
                    return true
                }
            }
        }

        return false
    }

    // MARK: - 打开设置页面
    private func openSettings() {
        DispatchQueue.main.async {
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
        }
    }
}
