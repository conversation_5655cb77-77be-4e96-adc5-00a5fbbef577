//
//  NotificationManager.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import Foundation
import UserNotifications
import SwiftUI
import SwiftData
import Combine

// MARK: - 通知管理器
class NotificationManager: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    @Published var isEnabled: Bool = false
    
    // MARK: - Private Properties
    private let notificationCenter = UNUserNotificationCenter.current()
    private var cancellables = Set<AnyCancellable>()



    // SwiftData 上下文，用于农历通知重新调度
    private var modelContext: ModelContext?
    
    // MARK: - Initialization
    override init() {
        super.init()
        notificationCenter.delegate = self
    }
    
    // MARK: - Setup
    func setup() {
        checkAuthorizationStatus()
        setupNotificationObservers()
    }

    private func setupNotificationObservers() {
        // 通知观察者已移除，因为不再需要
    }
    
    // MARK: - Authorization
    
    /// 请求通知权限
    func requestAuthorization() async -> Bool {
        do {
            let granted = try await notificationCenter.requestAuthorization(
                options: [.alert, .badge, .sound]
            )
            
            await MainActor.run {
                self.isEnabled = granted
                self.checkAuthorizationStatus()
            }
            
            return granted
        } catch {
            print("请求通知权限失败: \(error)")
            return false
        }
    }
    
    /// 检查当前授权状态
    func checkAuthorizationStatus() {
        notificationCenter.getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.authorizationStatus = settings.authorizationStatus
                self.isEnabled = settings.authorizationStatus == .authorized
            }
        }
    }
    
    // MARK: - Schedule Notifications
    


    /// 根据事件ID取消通知
    private func cancelNotification(eventId: String) {
        print("🗑️ 取消事件通知: \(eventId)")

        // 直接使用事件ID作为通知标识符进行取消
        notificationCenter.removePendingNotificationRequests(withIdentifiers: [eventId])
        print("✅ 已取消通知: \(eventId)")
    }
    
    /// 取消所有通知
    func cancelAllNotifications() {
        notificationCenter.removeAllPendingNotificationRequests()
    }
    
    // MARK: - Helper Methods
    


    private func calculateNotificationDates(for event: EventSwiftData) -> [Date]? {
        guard let nextOccurrence = event.nextOccurrenceDateForNotification else { return nil }

        var notificationDates: [Date] = []
        let calendar = Calendar.current

        // 解析通知时间
        let timeComponents = event.notificationTime.split(separator: ":")
        guard timeComponents.count == 2,
              let hour = Int(timeComponents[0]),
              let minute = Int(timeComponents[1]) else { return nil }

        // 获取提前通知类型
        let advanceType = event.advanceNotificationTypeEnum

        switch advanceType {
        case .none:
            return nil // 不发送通知

        case .onTime:
            // 准时提醒
            if let notificationDate = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: nextOccurrence) {
                notificationDates.append(notificationDate)
            }

        case .oneDay:
            // 提前1天
            if let dayBefore = calendar.date(byAdding: .day, value: -1, to: nextOccurrence),
               let notificationDate = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: dayBefore) {
                notificationDates.append(notificationDate)
            }

        case .threeDays:
            // 提前3天
            if let threeDaysBefore = calendar.date(byAdding: .day, value: -3, to: nextOccurrence),
               let notificationDate = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: threeDaysBefore) {
                notificationDates.append(notificationDate)
            }

        case .oneWeek:
            // 提前1周
            if let weekBefore = calendar.date(byAdding: .weekOfYear, value: -1, to: nextOccurrence),
               let notificationDate = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: weekBefore) {
                notificationDates.append(notificationDate)
            }
        }

        // 过滤掉已经过去的时间
        let now = Date()
        return notificationDates.filter { $0 > now }
    }



    private func generateNotificationBody(for event: EventSwiftData, date: Date) -> String {
        let calendar = Calendar.current
        let eventDate = event.nextOccurrenceDate ?? Date()
        let daysUntil = calendar.dateComponents([.day], from: calendar.startOfDay(for: Date()), to: calendar.startOfDay(for: eventDate)).day ?? 0

        if daysUntil == 0 {
            // 今天是纪念日
            switch event.cycleTypeEnum {
            case .never:
                if event.isLunar {
                    return "今天是「\(event.title)」的农历纪念日！"
                } else {
                    return "今天是「\(event.title)」！"
                }
            case .yearly:
                if event.isLunar {
                    return "今天是「\(event.title)」的农历纪念日！"
                } else {
                    return "今天是「\(event.title)」！"
                }
            case .monthly:
                return "今天是「\(event.title)」的月度纪念！"
            case .weekly:
                return "今天是「\(event.title)」的每周纪念！"
            case .daily:
                return "今天的「\(event.title)」提醒！"
            }
        } else if daysUntil == 1 {
            return "明天就是「\(event.title)」了，记得准备哦！"
        } else if daysUntil <= 7 {
            return "还有 \(daysUntil) 天就是「\(event.title)」了"
        } else {
            return "「\(event.title)」还有 \(daysUntil) 天"
        }
    }

    // MARK: - Badge Management

    /// 更新应用图标徽章
    func updateBadge(count: Int) {
        UNUserNotificationCenter.current().setBadgeCount(count)
    }

    /// 清除徽章
    func clearBadge() {
        updateBadge(count: 0)
        print("🔔 已清除应用通知badge")
    }

    // MARK: - Notification Management

    /// 获取所有待发送的通知
    func getPendingNotifications() async -> [UNNotificationRequest] {
        return await notificationCenter.pendingNotificationRequests()
    }

    /// 获取特定事件的待发送通知
    func getPendingNotifications(for eventId: String) async -> [UNNotificationRequest] {
        let allNotifications = await getPendingNotifications()
        return allNotifications.filter { request in
            if let userInfo = request.content.userInfo as? [String: Any],
               let notificationEventId = userInfo["eventId"] as? String {
                return notificationEventId == eventId
            }
            return false
        }
    }

    /// 为事件安排通知 (EventSwiftData)
    func scheduleNotification(for event: EventSwiftData) {
        // 实时检查通知权限状态
        notificationCenter.getNotificationSettings { settings in
            DispatchQueue.main.async {
                let isAuthorized = settings.authorizationStatus == .authorized

                // 更新isEnabled状态
                self.isEnabled = isAuthorized

                guard isAuthorized else {
                    print("⚠️ 通知权限未授权，跳过调度事件: \(event.title)")
                    return
                }

                print("🔄 重新调度通知: \(event.title)")

                // 先取消该事件的通知
                self.cancelNotification(for: event)

                // 直接添加新通知（不需要延迟，因为取消操作是同步的）
                self.addNotificationsForEvent(event)
            }
        }
    }

    /// 为事件添加通知的私有方法
    private func addNotificationsForEvent(_ event: EventSwiftData) {
        print("📝 为事件添加通知: \(event.title)")
        print("📝 通知时间字符串: \(event.notificationTime)")
        print("📝 提前通知类型: \(event.advanceNotificationTypeEnum.displayName)")

        // 对于农历事件，使用特殊的通知调度逻辑
        if event.isLunar {
            scheduleNotificationForLunarEvent(event)
            return
        }

        // 计算通知时间（公历事件）
        guard let notificationDates = calculateNotificationDates(for: event) else {
            print("❌ 无法计算通知时间")
            return
        }

        print("📅 计算出 \(notificationDates.count) 个通知时间")

        // 每个事件只有一个通知，直接使用事件ID作为通知标识符
        if let notificationDate = notificationDates.first {
            let identifier = event.id

            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            print("⏰ 准备添加通知: \(formatter.string(from: notificationDate))")

            let content = UNMutableNotificationContent()
            content.title = "纪念日提醒"
            content.body = generateNotificationBody(for: event, date: notificationDate)
            content.sound = .default
            // badge将在异步任务中设置

            // 添加用户信息
            content.userInfo = [
                "eventId": event.id,
                "eventTitle": event.title
            ]

            // 创建触发器 - 农历和公历事件需要不同的处理方式
            let calendar = Calendar.current
            var components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: notificationDate)
            var shouldRepeat = false

            if event.isLunar {
                // 农历事件：不使用系统重复，只设置下一次通知
                shouldRepeat = false
                print("🌙 农历事件通知：不使用系统重复，只设置下次发生时间")
            } else {
                // 公历事件：根据循环类型设置重复
                switch event.cycleTypeEnum {
                case .never:
                    // 永不重复：不使用系统重复，只设置一次性通知
                    shouldRepeat = false
                    print("🔄 一次性事件通知：不使用系统重复")
                case .yearly:
                    // 每年重复：只保留月、日、时、分
                    components.year = nil
                    shouldRepeat = true
                case .monthly:
                    // 每月重复：只保留日、时、分
                    components.year = nil
                    components.month = nil
                    shouldRepeat = true
                case .weekly:
                    // 每周重复：使用weekday
                    components = calendar.dateComponents([.weekday, .hour, .minute], from: notificationDate)
                    shouldRepeat = true
                case .daily:
                    // 每天重复：只保留时、分
                    components = calendar.dateComponents([.hour, .minute], from: notificationDate)
                    shouldRepeat = true
                }
            }

            let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: shouldRepeat)
            print("🔄 通知重复设置: \(shouldRepeat ? "是" : "否") (事件类型: \(event.isLunar ? "农历" : "公历"), 循环: \(event.cycleTypeEnum.displayName))")

            // 设置badge为1
            content.badge = 1

            // 创建请求
            let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)

            // 添加通知
            notificationCenter.add(request) { error in
                if let error = error {
                    print("❌ 添加通知失败 \(identifier): \(error)")
                } else {
                    print("✅ 成功添加通知 \(identifier): \(formatter.string(from: notificationDate))")
                }
            }
        }
    }

    /// 取消事件的通知 (EventSwiftData)
    func cancelNotification(for event: EventSwiftData) {
        cancelNotification(eventId: event.id)
    }



    /// 重新安排所有事件的通知 (EventSwiftData)
    func rescheduleAllNotifications(for events: [EventSwiftData]) {
        // 取消所有现有通知
        cancelAllNotifications()

        // 为每个启用通知的事件重新安排通知
        for event in events {
            if event.isNotificationEnabled {
                scheduleNotification(for: event)
            }
        }
    }

    /// 获取通知统计信息
    func getNotificationStats() async -> NotificationStats {
        let pendingNotifications = await getPendingNotifications()
        let todayNotifications = pendingNotifications.filter { request in
            guard let trigger = request.trigger as? UNCalendarNotificationTrigger,
                  let triggerDate = trigger.nextTriggerDate() else { return false }
            return Calendar.current.isDateInToday(triggerDate)
        }

        return NotificationStats(
            totalPending: pendingNotifications.count,
            todayNotifications: todayNotifications.count
        )
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension NotificationManager: UNUserNotificationCenterDelegate {
    
    /// 应用在前台时收到通知
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // 在前台也显示通知
        completionHandler([.banner, .sound, .badge])
    }
    
    /// 用户点击通知
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo

        if let eventId = userInfo["eventId"] as? String {
            // 发送通知，让应用导航到对应的事件
            NotificationCenter.default.post(
                name: .didTapNotification,
                object: nil,
                userInfo: ["eventId": eventId]
            )

            // 处理农历事件的重新调度
            handleLunarEventRescheduling(eventId: eventId)
        }

        completionHandler()
    }

    /// 处理农历事件的重新调度
    private func handleLunarEventRescheduling(eventId: String) {
        var contextToUse: ModelContext?

        if let modelContext = modelContext {
            contextToUse = modelContext
        } else {
            // 如果ModelContext未设置，尝试创建临时的
            print("⚠️ ModelContext 未设置，尝试创建临时ModelContext")
            do {
                let container = try ModelContainer(for: EventSwiftData.self)
                contextToUse = ModelContext(container)
                print("✅ 临时ModelContext创建成功")
            } catch {
                print("❌ 无法创建临时ModelContext: \(error)")
                return
            }
        }

        guard let context = contextToUse else {
            print("❌ 无法获取ModelContext，跳过农历通知重新调度")
            return
        }

        // 查找对应的事件
        let descriptor = FetchDescriptor<EventSwiftData>(
            predicate: #Predicate { event in
                event.id == eventId
            }
        )

        do {
            let events = try context.fetch(descriptor)
            if let event = events.first, event.isLunar && event.isNotificationEnabled {
                print("🌙 重新调度农历事件通知: \(event.title)")

                // 对于农历事件，需要强制重新计算下次发生日期
                // 因为当前的nextOccurrenceDate可能已经过期
                print("🔄 强制重新计算农历事件的下次发生日期")

                // 延迟一秒后重新调度，确保当前通知已处理完毕
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.scheduleNotificationForLunarEvent(event)
                }
            }
        } catch {
            print("❌ 查找农历事件失败: \(error)")
        }
    }

    /// 设置 ModelContext（由应用启动时调用）
    func setModelContext(_ context: ModelContext) {
        self.modelContext = context
    }

    /// 为农历事件调度通知（按照用户建议的简化逻辑）
    private func scheduleNotificationForLunarEvent(_ event: EventSwiftData) {
        print("🌙 开始为农历事件调度通知: \(event.title)")

        // 实时检查通知权限状态
        notificationCenter.getNotificationSettings { settings in
            DispatchQueue.main.async {
                let isAuthorized = settings.authorizationStatus == .authorized

                guard isAuthorized else {
                    print("⚠️ 通知权限未授权，跳过调度农历事件: \(event.title)")
                    return
                }

                // 先取消该事件的通知
                self.cancelNotification(for: event)

                // 农历通知功能暂时不可用（需要LunarCalendarManager）
                print("❌ 农历通知功能暂时不可用")
                return
            }
        }
    }

    /// 安排下次农历通知
    private func scheduleNextLunarNotification(_ event: EventSwiftData, lunarComponents: (year: Int, month: Int, day: Int, isLeapMonth: Bool)) {
        print("🔄 农历通知功能暂时不可用（需要LunarCalendarManager）")
        return
    }

    /// 为农历事件添加通知（使用指定的下次发生日期）
    private func addNotificationsForLunarEvent(_ event: EventSwiftData, nextOccurrenceDate: Date) {
        print("📝 为农历事件添加通知: \(event.title)")
        print("📝 使用下次发生日期: \(DateFormatter.localizedString(from: nextOccurrenceDate, dateStyle: .medium, timeStyle: .short))")

        // 解析通知时间
        let timeComponents = event.notificationTime.split(separator: ":")
        guard timeComponents.count == 2,
              let hour = Int(timeComponents[0]),
              let minute = Int(timeComponents[1]) else {
            print("❌ 无法解析通知时间: \(event.notificationTime)")
            return
        }

        let calendar = Calendar.current
        let advanceType = event.advanceNotificationTypeEnum

        var notificationDate: Date?

        switch advanceType {
        case .none:
            print("📝 提前通知类型: 不提醒")
            return

        case .onTime:
            print("📝 提前通知类型: 准时提醒")
            notificationDate = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: nextOccurrenceDate)

        case .oneDay:
            print("📝 提前通知类型: 提前1天")
            if let dayBefore = calendar.date(byAdding: .day, value: -1, to: nextOccurrenceDate) {
                notificationDate = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: dayBefore)
            }

        case .threeDays:
            print("📝 提前通知类型: 提前3天")
            if let threeDaysBefore = calendar.date(byAdding: .day, value: -3, to: nextOccurrenceDate) {
                notificationDate = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: threeDaysBefore)
            }

        case .oneWeek:
            print("📝 提前通知类型: 提前1周")
            if let weekBefore = calendar.date(byAdding: .weekOfYear, value: -1, to: nextOccurrenceDate) {
                notificationDate = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: weekBefore)
            }
        }

        guard let finalNotificationDate = notificationDate else {
            print("❌ 无法计算通知日期")
            return
        }

        // 检查通知时间是否已过期
        let now = Date()
        if finalNotificationDate <= now {
            print("⚠️ 通知时间已过期: \(DateFormatter.localizedString(from: finalNotificationDate, dateStyle: .medium, timeStyle: .short))")
            return
        }

        print("✅ 最终通知时间: \(DateFormatter.localizedString(from: finalNotificationDate, dateStyle: .medium, timeStyle: .short))")

        // 创建通知内容
        let content = UNMutableNotificationContent()
        content.title = "纪念日提醒"
        content.body = generateNotificationBody(for: event, date: finalNotificationDate)
        content.sound = .default

        // 创建日期组件触发器（农历事件不使用重复）
        let dateComponents = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: finalNotificationDate)
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: false)

        // 设置badge为1
        content.badge = 1

        // 创建通知请求
        let request = UNNotificationRequest(
            identifier: event.id,
            content: content,
            trigger: trigger
        )

        // 添加通知
        notificationCenter.add(request) { error in
            if let error = error {
                print("❌ 添加农历通知失败: \(error)")
            } else {
                print("✅ 农历通知添加成功: \(event.title)")
            }
        }
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let didTapNotification = Notification.Name("DidTapNotification")
}

// MARK: - Notification Permission Helper
extension NotificationManager {
    
    /// 检查是否需要显示权限请求
    var shouldShowPermissionRequest: Bool {
        return authorizationStatus == .notDetermined
    }
    
    /// 检查是否被拒绝
    var isPermissionDenied: Bool {
        return authorizationStatus == .denied
    }
    
    /// 打开设置页面
    func openSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }

    /// 检查通知是否过期并清理
    func cleanupExpiredNotifications() async {
        let pendingNotifications = await getPendingNotifications()
        let now = Date()

        var expiredIdentifiers: [String] = []

        for request in pendingNotifications {
            if let trigger = request.trigger as? UNCalendarNotificationTrigger,
               let triggerDate = trigger.nextTriggerDate(),
               triggerDate < now {
                expiredIdentifiers.append(request.identifier)
            }
        }

        if !expiredIdentifiers.isEmpty {
            notificationCenter.removePendingNotificationRequests(withIdentifiers: expiredIdentifiers)
        }
    }
}

// MARK: - 通知统计信息
struct NotificationStats {
    let totalPending: Int
    let todayNotifications: Int
}

// MARK: - 通知错误类型
enum NotificationError: Error, LocalizedError {
    case permissionDenied
    case invalidDate
    case schedulingFailed

    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "通知权限被拒绝"
        case .invalidDate:
            return "无效的日期"
        case .schedulingFailed:
            return "通知调度失败"
        }
    }
}
