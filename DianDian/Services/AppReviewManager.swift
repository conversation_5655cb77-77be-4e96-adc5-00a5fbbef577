//
//  AppReviewManager.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import Foundation
import StoreKit
import SwiftUI

// MARK: - 应用评分管理器
class AppReviewManager {
    static let shared = AppReviewManager()
    
    private let userDefaults = UserDefaults.standard
    
    // UserDefaults 键
    private struct Keys {
        static let launchCount = "AppLaunchCount"
        static let lastReviewRequestDate = "LastReviewRequestDate"
        static let hasRatedApp = "HasRatedApp"
        static let eventCreatedCount = "EventCreatedCount"
        static let reviewRequestCount = "ReviewRequestCount"
    }
    
    private init() {}
    
    // MARK: - 应用启动计数
    func incrementLaunchCount() {
        let currentCount = userDefaults.integer(forKey: Keys.launchCount)
        userDefaults.set(currentCount + 1, forKey: Keys.launchCount)
    }
    
    // MARK: - 事件创建计数
    func incrementEventCreatedCount() {
        let currentCount = userDefaults.integer(forKey: Keys.eventCreatedCount)
        userDefaults.set(currentCount + 1, forKey: Keys.eventCreatedCount)
        
        // 检查是否应该请求评分
        checkAndRequestReview()
    }
    
    // MARK: - 检查并请求评分
    func checkAndRequestReview() {
        guard shouldRequestReview() else { return }
        
        DispatchQueue.main.async {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                if #available(iOS 18.0, *) {
                    AppStore.requestReview(in: windowScene)
                } else {
                    SKStoreReviewController.requestReview(in: windowScene)
                }
                self.recordReviewRequest()
            }
        }
    }
    
    // MARK: - 手动请求评分
    func requestReview() {
        DispatchQueue.main.async {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                if #available(iOS 18.0, *) {
                    AppStore.requestReview(in: windowScene)
                } else {
                    SKStoreReviewController.requestReview(in: windowScene)
                }
                self.recordReviewRequest()
            }
        }
    }
    
    // MARK: - 打开 App Store 评分页面
    func openAppStoreReview() {
        guard let url = URL(string: "https://apps.apple.com/app/id123456789?action=write-review") else { return }
        
        DispatchQueue.main.async {
            UIApplication.shared.open(url)
            self.userDefaults.set(true, forKey: Keys.hasRatedApp)
        }
    }
    
    // MARK: - 私有方法
    
    private func shouldRequestReview() -> Bool {
        // 如果用户已经评分过，不再请求
        if userDefaults.bool(forKey: Keys.hasRatedApp) {
            return false
        }
        
        // 检查请求次数限制（每年最多3次）
        let requestCount = userDefaults.integer(forKey: Keys.reviewRequestCount)
        if requestCount >= 3 {
            return false
        }
        
        // 检查距离上次请求的时间间隔（至少30天）
        if let lastRequestDate = userDefaults.object(forKey: Keys.lastReviewRequestDate) as? Date {
            let daysSinceLastRequest = Calendar.current.dateComponents([.day], from: lastRequestDate, to: Date()).day ?? 0
            if daysSinceLastRequest < 30 {
                return false
            }
        }
        
        // 检查应用使用情况
        let launchCount = userDefaults.integer(forKey: Keys.launchCount)
        let eventCount = userDefaults.integer(forKey: Keys.eventCreatedCount)
        
        // 条件1：启动次数超过10次且创建了至少3个事件
        if launchCount >= 10 && eventCount >= 3 {
            return true
        }
        
        // 条件2：启动次数超过20次
        if launchCount >= 20 {
            return true
        }
        
        // 条件3：创建了至少10个事件
        if eventCount >= 10 {
            return true
        }
        
        return false
    }
    
    private func recordReviewRequest() {
        userDefaults.set(Date(), forKey: Keys.lastReviewRequestDate)
        
        let currentCount = userDefaults.integer(forKey: Keys.reviewRequestCount)
        userDefaults.set(currentCount + 1, forKey: Keys.reviewRequestCount)
    }
    
    // MARK: - 统计信息
    
    var launchCount: Int {
        return userDefaults.integer(forKey: Keys.launchCount)
    }
    
    var eventCreatedCount: Int {
        return userDefaults.integer(forKey: Keys.eventCreatedCount)
    }
    
    var hasRatedApp: Bool {
        return userDefaults.bool(forKey: Keys.hasRatedApp)
    }
    
    var lastReviewRequestDate: Date? {
        return userDefaults.object(forKey: Keys.lastReviewRequestDate) as? Date
    }
    
    // MARK: - 重置方法（用于测试）
    
    func resetReviewData() {
        userDefaults.removeObject(forKey: Keys.launchCount)
        userDefaults.removeObject(forKey: Keys.lastReviewRequestDate)
        userDefaults.removeObject(forKey: Keys.hasRatedApp)
        userDefaults.removeObject(forKey: Keys.eventCreatedCount)
        userDefaults.removeObject(forKey: Keys.reviewRequestCount)
    }
}

// MARK: - 评分提示视图
struct RatingPromptView: View {
    @Environment(\.dismiss) private var dismiss
    let onRate: () -> Void
    let onLater: () -> Void
    let onNever: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            // 图标
            Image(systemName: "heart.fill")
                .font(.system(size: 60))
                .foregroundColor(.pink)
            
            // 标题和描述
            VStack(spacing: 12) {
                Text("喜欢DianDian吗？")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("您的评价对我们非常重要，\n帮助我们改进应用体验")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // 星星评分显示
            HStack(spacing: 8) {
                ForEach(0..<5) { _ in
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                        .font(.title3)
                }
            }
            
            // 按钮
            VStack(spacing: 12) {
                Button(action: {
                    onRate()
                    dismiss()
                }) {
                    Text("去评分")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.pink)
                        .cornerRadius(25)
                }
                
                HStack(spacing: 20) {
                    Button("稍后提醒") {
                        onLater()
                        dismiss()
                    }
                    .foregroundColor(.secondary)
                    
                    Button("不再提醒") {
                        onNever()
                        dismiss()
                    }
                    .foregroundColor(.secondary)
                }
                .font(.subheadline)
            }
        }
        .padding(30)
        .background(Color(.systemBackground))
        .cornerRadius(20)
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        .padding(.horizontal, 40)
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        Color.black.opacity(0.3)
            .ignoresSafeArea()
        
        RatingPromptView(
            onRate: { print("Rate") },
            onLater: { print("Later") },
            onNever: { print("Never") }
        )
    }
}
