//
//  DianDianApp.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import SwiftUI
import SwiftData
import UserNotifications

@main
struct DianDianApp: App {

    // MARK: - State Objects
    @StateObject private var appState = AppStateManager()
    @StateObject private var notificationManager = NotificationManager()
    @StateObject private var securityManager = SecurityManager()


    // SwiftData 容器（支持App Group共享）
    private var modelContainer: ModelContainer = {
        do {
            // App Group标识符
            let appGroupIdentifier = "group.com.iamlihongking.dotdot"

            // 尝试使用App Group共享存储
            if let appGroupURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) {
                let storeURL = appGroupURL.appendingPathComponent("DianDian.sqlite")
                print("✅ 主应用: 使用App Group共享数据库: \(storeURL.path)")

                let configuration = ModelConfiguration(url: storeURL)
                let container = try ModelContainer(for: EventSwiftData.self, configurations: configuration)
                print("✅ SwiftData 容器创建成功（共享存储）")
                return container
            } else {
                print("⚠️ 主应用: 无法获取App Group目录，使用默认存储")
                let container = try ModelContainer(for: EventSwiftData.self)
                print("✅ SwiftData 容器创建成功（默认存储）")
                return container
            }
        } catch {
            print("❌ 无法创建 SwiftData 容器: \(error)")
            fatalError("SwiftData 初始化失败")
        }
    }()

    // MARK: - App Lifecycle
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appState)
                .environmentObject(notificationManager)
                .environmentObject(securityManager)

                .onAppear {
                    setupApplication()
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
                    // 应用进入前台时重置认证状态
                    securityManager.resetAuthenticationOnBackground()
                }
                .onOpenURL { url in
                    // 处理Widget URL
                    appState.handleWidgetURL(url)
                }
                .task {
                    // 数据迁移已完成，不再需要迁移服务
                    print("✅ 应用启动完成，使用SwiftData")

                    // 在这里也设置 ModelContext，确保更早设置
                    let context = ModelContext(modelContainer)
                    notificationManager.setModelContext(context)
                    print("✅ 在 task 中设置了 NotificationManager 的 ModelContext")
                }
        }
        .modelContainer(modelContainer)
    }

    // MARK: - Setup Methods
    private func setupApplication() {
        // 请求通知权限
        requestNotificationPermission()

        // 初始化应用状态
        appState.initializeApp()

        // 设置通知管理器
        notificationManager.setup()

        // 清除应用启动时的通知badge
        notificationManager.clearBadge()

        // 设置 NotificationManager 的 ModelContext
        let context = ModelContext(modelContainer)
        notificationManager.setModelContext(context)

        // 增加启动计数
        AppReviewManager.shared.incrementLaunchCount()
    }

    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                appState.notificationPermissionGranted = granted
                if let error = error {
                    print("通知权限请求失败: \(error)")
                }
            }
        }
    }
}
