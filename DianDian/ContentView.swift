//
//  ContentView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject var appState: AppStateManager
    @EnvironmentObject var securityManager: SecurityManager
    @State private var isShowingLaunchScreen = true

    var body: some View {
        ZStack {
            if isShowingLaunchScreen {
                LaunchScreenView()
                    .onAppear {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                            withAnimation(.easeInOut(duration: 0.5)) {
                                isShowingLaunchScreen = false
                            }
                        }
                    }
            } else {
                if securityManager.isSecurityEnabled && !securityManager.isAuthenticated {
                    SecurityLockView()
                } else if appState.isFirstLaunch {
                    OnboardingView()
                } else {
                    MainTabView()
                }
            }
        }
    }
}

// MARK: - Launch Screen
struct LaunchScreenView: View {
    var body: some View {
        ZStack {
            Color(.systemBackground)
                .ignoresSafeArea()

            VStack(spacing: 20) {
                Image("Logo")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 90, height: 90)

                Text("DianDian")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("记录每一个重要时刻")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
    }
}

// MARK: - Onboarding View
struct OnboardingView: View {
    @EnvironmentObject var appState: AppStateManager

    var body: some View {
        VStack(spacing: 40) {
            Spacer()

            Image("Logo")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 90, height: 90)

            VStack(spacing: 16) {
                Text("欢迎使用DianDian")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text("记录生活中的每一个重要时刻\n让美好的回忆永远陪伴")
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Button(action: {
                appState.completeOnboarding()
            }) {
                Text("开始使用")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.pink)
                    .cornerRadius(25)
            }
            .padding(.horizontal, 40)

            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

#Preview {
    ContentView()
        .environmentObject(AppStateManager())
        .environmentObject(SecurityManager())
}
