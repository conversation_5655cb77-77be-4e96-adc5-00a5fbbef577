//
//  DetailComponents.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/28.
//

import SwiftUI

// MARK: - 详情区域组件
struct DetailSection1<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text(title)
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.horizontal, 16)
                .padding(.top, 16)
                .padding(.bottom, 8)
            
            VStack(alignment: .leading, spacing: 0) {
                content
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray5))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray5), lineWidth: 0.5)
                )
        )
    }
}

struct DetailRow: View {
    let title: String
    let value: String
    let icon: String
    let isMultiline: Bool

    init(title: String, value: String, icon: String, isMultiline: Bool = false) {
        self.title = title
        self.value = value
        self.icon = icon
        self.isMultiline = isMultiline
    }

    var body: some View {
        if isMultiline {
            // 多行文本布局
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: icon)
                        .foregroundColor(.secondary)
                        .frame(width: 20)

                    Text(title)
                        .foregroundColor(.secondary)

                    Spacer()
                }

                Text(value)
                    .font(.body)
                    .foregroundColor(.primary)
                    .fixedSize(horizontal: false, vertical: true)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.leading, 28) // 与图标对齐
            }
            .padding(.vertical, 8)
        } else {
            // 单行键值对布局
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.secondary)
                    .frame(width: 20)

                Text(title)
                    .foregroundColor(.secondary)

                Spacer()

                Text(value)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            .padding(.vertical, 8)
        }
    }
}
