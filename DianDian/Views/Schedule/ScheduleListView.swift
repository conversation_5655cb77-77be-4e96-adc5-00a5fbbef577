//
//  ScheduleListView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/28.
//

import SwiftUI

// MARK: - 日程列表视图
struct ScheduleListView: View {
    @StateObject private var scheduleService = ScheduleDataService()
    @State private var searchText = ""
    @State private var selectedSchedule: ScheduleModel?
    @State private var showingScheduleDetail = false
    
    private var filteredSchedules: [ScheduleModel] {
        if searchText.isEmpty {
            return scheduleService.schedules
        } else {
            return scheduleService.searchSchedules(query: searchText)
        }
    }
    
    private var groupedSchedules: [ScheduleGroup] {
        let today = Calendar.current.startOfDay(for: Date())
        let groupedDict = Dictionary(grouping: filteredSchedules) { schedule in
            Calendar.current.startOfDay(for: schedule.startDate)
        }

        let groups = groupedDict.map { date, schedules in
            ScheduleGroup(date: date, schedules: schedules.sorted { $0.startDate < $1.startDate })
        }

        // 分离今天及未来的日程和已过的日程
        let futureGroups = groups.filter { $0.date >= today }.sorted { $0.date < $1.date }
        let pastGroups = groups.filter { $0.date < today }.sorted { $0.date > $1.date } // 已过的日程按时间倒序

        // 先显示今天及未来的日程，再显示已过的日程
        return futureGroups + pastGroups
    }
    
    var body: some View {
        NavigationView {
            // 检查日历权限状态
            if !scheduleService.calendarAccessGranted {
                // 显示未授权界面
                CalendarPermissionView(
                    onRequestPermission: {
                        scheduleService.requestCalendarPermission()
                    }
                )
                .background(Color(.systemGroupedBackground))
                .navigationTitle("日程")
                .navigationBarTitleDisplayMode(.large)
            } else {
                // 有权限时显示正常界面
                scheduleContentView
                    .background(Color(.systemGroupedBackground))
                    .navigationTitle("日程")
                    .navigationBarTitleDisplayMode(.large)
            }
        }
        .onAppear {
            if scheduleService.calendarAccessGranted {
                scheduleService.loadSchedules()
            }
        }
        .sheet(item: $selectedSchedule) { schedule in
            ScheduleDetailView(schedule: schedule)
        }
    }
    
    @ViewBuilder
    private var scheduleContentView: some View {
        VStack(spacing: 0) {
            // 搜索栏
            EnhancedSearchBar(
                text: $searchText,
                placeholder: "search.schedule.placeholder".localized
            )
            
            // 主内容
            if scheduleService.isLoading {
                LoadingView()
            } else if filteredSchedules.isEmpty {
                ScheduleEmptyStateView(isSearching: !searchText.isEmpty)
            } else {
                scheduleListView
            }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    scheduleService.loadSchedules()
                }) {
                    Image(systemName: "arrow.clockwise")
                }
            }
        }
    }
    
    @ViewBuilder
    private var scheduleListView: some View {
        List {
            // 今日日程
            let todaySchedules = scheduleService.getTodaySchedules()
            if !todaySchedules.isEmpty && searchText.isEmpty {
                Section("今日日程") {
                    ForEach(todaySchedules, id: \.id) { schedule in
                        ScheduleRowView(schedule: schedule)
                            .listRowInsets(LayoutConstants.standardListRowInsets)
                            .listRowSeparator(.hidden)
                            .listRowBackground(Color.clear)
                            .onTapGesture {
                                selectedSchedule = schedule
                                showingScheduleDetail = true
                            }
                    }
                }
            }
            
            // 即将到来的日程
            let upcomingSchedules = scheduleService.getUpcomingSchedules()
            if !upcomingSchedules.isEmpty && searchText.isEmpty {
                Section("即将到来") {
                    ForEach(upcomingSchedules, id: \.id) { schedule in
                        ScheduleRowView(schedule: schedule)
                            .listRowInsets(LayoutConstants.standardListRowInsets)
                            .listRowSeparator(.hidden)
                            .listRowBackground(Color.clear)
                            .onTapGesture {
                                selectedSchedule = schedule
                                showingScheduleDetail = true
                            }
                    }
                }
            }
            
            // 按日期分组的日程
            if searchText.isEmpty {
                ForEach(groupedSchedules, id: \.date) { group in
                    Section(header: ScheduleDateHeaderView(group: group)) {
                        ForEach(group.schedules, id: \.id) { schedule in
                            ScheduleRowView(schedule: schedule)
                                .listRowInsets(LayoutConstants.standardListRowInsets)
                                .listRowSeparator(.hidden)
                                .listRowBackground(Color.clear)
                                .onTapGesture {
                                    selectedSchedule = schedule
                                    showingScheduleDetail = true
                                }
                        }
                    }
                }
            } else {
                // 搜索结果
                Section("search.results".localized) {
                    ForEach(filteredSchedules, id: \.id) { schedule in
                        ScheduleRowView(schedule: schedule)
                            .listRowInsets(LayoutConstants.standardListRowInsets)
                            .listRowSeparator(.hidden)
                            .listRowBackground(Color.clear)
                            .onTapGesture {
                                selectedSchedule = schedule
                                showingScheduleDetail = true
                            }
                    }
                }
            }
        }
        .listStyle(PlainListStyle())
        .refreshable {
            scheduleService.loadSchedules()
        }
    }
}

// MARK: - 日程日期头部视图
struct ScheduleDateHeaderView: View {
    let group: ScheduleGroup

    private var isPastDate: Bool {
        let today = Calendar.current.startOfDay(for: Date())
        return group.date < today
    }

    var body: some View {
        HStack {
            HStack(spacing: 6) {
                if isPastDate {
                    Image(systemName: "clock.arrow.circlepath")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.secondary)
                }

                Text(group.formattedDate)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(isPastDate ? .secondary : .primary)
            }

            Spacer()

            Text("\(group.schedules.count) 个日程")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - 日程空状态视图
struct ScheduleEmptyStateView: View {
    let isSearching: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: isSearching ? "magnifyingglass" : "calendar.badge.clock")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text(isSearching ? "schedule.empty.no.related.schedule".localized : "schedule.empty.no.schedule".localized)
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.primary)

            Text(isSearching ? "schedule.empty.try.other.keywords".localized : "schedule.empty.no.arrangements".localized)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
}

// MARK: - 预览
#Preview {
    ScheduleListView()
}
