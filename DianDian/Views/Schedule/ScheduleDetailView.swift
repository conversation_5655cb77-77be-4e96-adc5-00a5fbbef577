//
//  ScheduleDetailView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/28.
//

import SwiftUI

// MARK: - 日程详情视图
struct ScheduleDetailView: View {
    let schedule: ScheduleModel
    @Environment(\.dismiss) private var dismiss
    
    private var scheduleColor: Color {
        schedule.themeColor
    }
    
    private var statusColor: Color {
        if schedule.isToday {
            return scheduleColor
        } else if schedule.isFinished {
            return .secondary
        } else {
            return scheduleColor
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 日程头部信息
                    VStack(spacing: 16) {
                        // 图标和标题
                        VStack(spacing: 12) {
                            ZStack {
                                Circle()
                                    .fill(scheduleColor.opacity(0.2))
                                    .frame(width: 80, height: 80)

                                Image(systemName: schedule.isAllDay ? "calendar" : "clock")
                                    .font(.system(size: 32))
                                    .foregroundColor(scheduleColor)
                            }
                            
                            Text(schedule.title)
                                .font(.title2)
                                .fontWeight(.bold)
                                .multilineTextAlignment(.center)
                        }

                        // 时间状态显示
                        VStack(spacing: 4) {
                            if schedule.isAllDay {
                                Text("schedule.all.day".localized)
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(statusColor)
                                
                                Text(formatDate(schedule.startDate))
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            } else {
                                Text(schedule.timeOfDay)
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(statusColor)

                                Text("\(formatTime(schedule.startDate)) - \(formatTime(schedule.endDate))")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                
                                Text("schedule.duration".localized(schedule.durationText))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding(.top, 20)

                    // 详细信息卡片
                    VStack(spacing: 16) {
                        // 基本信息
                        EventInfoCard(title: "section.basic.info".localized) {
                            if schedule.isAllDay {
                                EventInfoRow(
                                    label: "schedule.date".localized,
                                    value: formatDate(schedule.startDate),
                                    icon: "calendar",
                                    iconColor: scheduleColor
                                )
                            } else {
                                EventInfoRow(
                                    label: "schedule.start.time".localized,
                                    value: formatDateTime(schedule.startDate),
                                    icon: "clock",
                                    iconColor: scheduleColor
                                )
                                EventInfoRow(
                                    label: "schedule.end.time".localized,
                                    value: formatDateTime(schedule.endDate),
                                    icon: "clock.arrow.circlepath",
                                    iconColor: scheduleColor
                                )
                                EventInfoRow(
                                    label: "schedule.duration.label".localized,
                                    value: schedule.durationText,
                                    icon: "timer",
                                    iconColor: scheduleColor
                                )
                            }
                            EventInfoRow(
                                label: "schedule.calendar".localized,
                                value: schedule.calendar,
                                icon: "calendar.badge.plus",
                                iconColor: scheduleColor
                            )
                            EventInfoRow(
                                label: "schedule.status".localized,
                                value: schedule.isFinished ? "schedule.status.finished".localized : (schedule.isOngoing ? "schedule.status.ongoing".localized : "schedule.status.upcoming".localized),
                                icon: schedule.isFinished ? "checkmark.circle" : (schedule.isOngoing ? "play.circle" : "clock"),
                                iconColor: scheduleColor
                            )
                        }

                        // 位置信息
                        if let location = schedule.location, !location.isEmpty {
                            EventInfoCard(title: "schedule.location".localized) {
                                EventInfoRow(
                                    label: "schedule.location.address".localized,
                                    value: location,
                                    icon: "location",
                                    iconColor: scheduleColor
                                )
                            }
                        }

                        // 备注信息
                        if let notes = schedule.notes, !notes.isEmpty {
                            EventInfoCard(title: "event.note".localized) {
                                Text(notes)
                                    .font(.body)
                                    .foregroundColor(.primary)
                                    .multilineTextAlignment(.leading)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .padding(.vertical, 8)
                            }
                        }
                    }
                    .standardHorizontalPadding()

                    Spacer(minLength: 20)
                }
                .padding(.top, 10)
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle(schedule.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("button.close".localized) {
                        dismiss()
                    }
                }
            }
        }
    }

    // MARK: - 格式化方法
    private func formatDate(_ date: Date) -> String {
        let yearMonthDayFormatter = createYearMonthDayFormatter()
        let weekdayFormatter = createWeekdayFormatter()
        let dateString = yearMonthDayFormatter.string(from: date)
        let weekday = weekdayFormatter.string(from: date)
        return "\(dateString) \(weekday)"
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = createTimeFormatter()
        return formatter.string(from: date)
    }

    private func formatDateTime(_ date: Date) -> String {
        let formatter = createDateTimeFormatter()
        return formatter.string(from: date)
    }

    private func createYearMonthDayFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        formatter.locale = Locale.current
        return formatter
    }

    private func createWeekdayFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE"
        formatter.locale = Locale.current
        return formatter
    }

    private func createTimeFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale.current
        return formatter
    }

    private func createDateTimeFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale.current
        return formatter
    }
}

// MARK: - 预览
#Preview {
    let sampleSchedule = ScheduleModel(
        id: "sample",
        title: "会议",
        startDate: Date(),
        endDate: Date().addingTimeInterval(3600),
        isAllDay: false,
        location: "会议室",
        notes: "重要会议",
        calendar: "工作",
        calendarColor: "#007AFF"
    )
    ScheduleDetailView(schedule: sampleSchedule)
}
