//
//  ScheduleRowView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/28.
//

import SwiftUI

// MARK: - 日程行视图
struct ScheduleRowView: View {
    let schedule: ScheduleModel

    private var timeBarColor: Color {
        if schedule.isFinished {
            return schedule.themeColor.opacity(0.5)
        }
        return schedule.themeColor
    }

    private var textOpacity: Double {
        schedule.isFinished ? 0.6 : 1.0
    }
    
    var body: some View {
        HStack(spacing: 12) {
            // 左侧时间条
            VStack(spacing: 4) {
                Text(schedule.formattedStartTime)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
                    .opacity(textOpacity)

                if !schedule.formattedEndTime.isEmpty {
                    Text(schedule.formattedEndTime)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                        .opacity(textOpacity)
                }
            }
            .frame(width: 70, alignment: .leading)
            
            // 时间条
            RoundedRectangle(cornerRadius: 3)
                .fill(timeBarColor)
                .frame(width: 6)
                .padding(.vertical, 4)

            // 内容区域
            VStack(alignment: .leading, spacing: 4) {
                // 标题
                HStack(spacing: 6) {
                    Text(schedule.title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                        .opacity(textOpacity)
                        .lineLimit(1)

                    if schedule.isFinished {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }
                }

                // 时间段描述
                Text(schedule.timeOfDay)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .opacity(textOpacity)
            }
            
            Spacer()
            
            // 右侧持续时间
            Text(schedule.durationText)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
                .opacity(textOpacity)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color("MainBgColor"))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray5), lineWidth: 0.5)
                )
        )
    }
}



// MARK: - 预览
#Preview("日程行视图") {
    VStack(spacing: 12) {
        ScheduleRowView(
            schedule: ScheduleModel(
                id: "1",
                title: "Nick Zhurenko",
                startDate: Calendar.current.date(from: DateComponents(hour: 10, minute: 0)) ?? Date(),
                endDate: Calendar.current.date(from: DateComponents(hour: 17, minute: 0)) ?? Date(),
                isAllDay: false,
                location: nil,
                notes: nil,
                calendar: "工作",
                calendarColor: "#4CAF50"
            )
        )
        
        ScheduleRowView(
            schedule: ScheduleModel(
                id: "2",
                title: "Rebekka Altair",
                startDate: Calendar.current.date(from: DateComponents(hour: 13, minute: 0)) ?? Date(),
                endDate: Calendar.current.date(from: DateComponents(hour: 19, minute: 0)) ?? Date(),
                isAllDay: false,
                location: nil,
                notes: nil,
                calendar: "个人",
                calendarColor: "#2196F3"
            )
        )
        
        ScheduleRowView(
            schedule: ScheduleModel(
                id: "3",
                title: "Stephan Side",
                startDate: Calendar.current.date(from: DateComponents(hour: 13, minute: 0)) ?? Date(),
                endDate: Calendar.current.date(from: DateComponents(hour: 19, minute: 0)) ?? Date(),
                isAllDay: false,
                location: nil,
                notes: nil,
                calendar: "会议",
                calendarColor: "#2196F3"
            )
        )
        
        ScheduleRowView(
            schedule: ScheduleModel(
                id: "4",
                title: "Stella Artohina",
                startDate: Calendar.current.date(from: DateComponents(hour: 19, minute: 0)) ?? Date(),
                endDate: Calendar.current.date(from: DateComponents(hour: 23, minute: 0)) ?? Date(),
                isAllDay: false,
                location: nil,
                notes: nil,
                calendar: "娱乐",
                calendarColor: "#FF5722"
            )
        )
        
        ScheduleRowView(
            schedule: ScheduleModel(
                id: "5",
                title: "Lisa Smith",
                startDate: Calendar.current.date(from: DateComponents(hour: 19, minute: 0)) ?? Date(),
                endDate: Calendar.current.date(from: DateComponents(hour: 23, minute: 0)) ?? Date(),
                isAllDay: false,
                location: nil,
                notes: nil,
                calendar: "社交",
                calendarColor: "#FF5722"
            )
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
