//
//  PremiumView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import SwiftUI

// MARK: - 高级版页面
struct PremiumView: View {
    @EnvironmentObject var appState: AppStateManager
    @Environment(\.dismiss) private var dismiss
    @StateObject private var purchaseManager = PurchaseManager.shared

    @State private var isProcessingPurchase = false
    @State private var isProcessingRestore = false
    @State private var showingPurchaseAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 30) {
                    // 头部
                    VStack(spacing: 16) {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.yellow)
                        
                        Text("升级到高级版")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("解锁所有功能，享受完整体验")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    
                    // 功能对比
                    VStack(spacing: 16) {
                        Text("功能对比")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        FeatureComparisonView()
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // 产品价格
                    VStack(spacing: 16) {
                        Text("高级版价格")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)

                        VStack(spacing: 12) {
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("高级版")
                                        .font(.headline)
                                        .foregroundColor(.primary)

                                    Text("一次购买，永久使用")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }

                                Spacer()

                                VStack(alignment: .trailing, spacing: 2) {
                                    if purchaseManager.isLoading {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                    } else {
                                        Text(purchaseManager.premiumPrice)
                                            .font(.headline)
                                            .fontWeight(.bold)
                                            .foregroundColor(.pink)
                                    }
                                }
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.pink, lineWidth: 2)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color.pink.opacity(0.1))
                                    )
                            )
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // 购买按钮
                    VStack(spacing: 12) {
                        Button(action: {
                            purchasePremium()
                        }) {
                            HStack {
                                if isProcessingPurchase {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: purchaseManager.isPremiumUser ? "checkmark.circle.fill" : "crown.fill")
                                }

                                Text(purchaseButtonText)
                                    .fontWeight(.semibold)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(
                                LinearGradient(
                                    colors: purchaseManager.isPremiumUser ? [.green, .green] : [.pink, .purple],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(25)
                        }
                        .disabled(isProcessingPurchase || purchaseManager.isPremiumUser)

                        Button(action: {
                            restorePurchase()
                        }) {
                            HStack(spacing: 8) {
                                if isProcessingRestore {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .secondary))
                                        .scaleEffect(0.8)
                                }
                                Text(isProcessingRestore ? "恢复中..." : "恢复购买")
                            }
                        }
                        .foregroundColor(.secondary)
                        .disabled(isProcessingRestore || isProcessingPurchase)
                    }
                    
                    // 条款和隐私
                    VStack(spacing: 8) {
                        Text("购买即表示您同意我们的")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        HStack(spacing: 4) {
                            Button("服务条款") {
                                // 打开服务条款
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                            
                            Text("和")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Button("隐私政策") {
                                // 打开隐私政策
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                    }
                    .padding(.top, 20)
                }
                .padding()
            }
            .navigationTitle("高级版")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
            .alert("提示", isPresented: $showingPurchaseAlert) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    // MARK: - Computed Properties
    private var purchaseButtonText: String {
        if isProcessingPurchase {
            return "处理中..."
        } else if purchaseManager.isPremiumUser {
            return "已购买"
        } else {
            return "立即购买"
        }
    }

    // MARK: - Actions
    private func purchasePremium() {
        guard let product = purchaseManager.premiumProduct else {
            alertMessage = "产品信息加载失败，请稍后重试"
            showingPurchaseAlert = true
            return
        }

        isProcessingPurchase = true

        Task {
            do {
                let transaction = try await purchaseManager.purchase(product)

                await MainActor.run {
                    isProcessingPurchase = false

                    if transaction != nil {
                        // 购买成功，更新AppState
                        appState.purchasePremium()
                        alertMessage = "购买成功！感谢您的支持！"
                        showingPurchaseAlert = true
                    } else {
                        // 用户取消或其他情况
                        alertMessage = "购买已取消"
                        showingPurchaseAlert = true
                    }
                }
            } catch {
                await MainActor.run {
                    isProcessingPurchase = false
                    alertMessage = "购买失败：\(error.localizedDescription)"
                    showingPurchaseAlert = true
                }
            }
        }
    }

    private func restorePurchase() {
        isProcessingRestore = true

        Task {
            do {
                await purchaseManager.restorePurchases()

                await MainActor.run {
                    isProcessingRestore = false
                    let restored = appState.restorePurchase()
                    if restored {
                        alertMessage = "购买记录已恢复"
                        showingPurchaseAlert = true
                    } else {
                        alertMessage = "未找到购买记录"
                        showingPurchaseAlert = true
                    }
                }
            } catch {
                await MainActor.run {
                    isProcessingRestore = false
                    alertMessage = "恢复失败：\(error.localizedDescription)"
                    showingPurchaseAlert = true
                }
            }
        }
    }
}



// MARK: - 功能对比视图
struct FeatureComparisonView: View {
    private let features = [
        ("纪念日", true, true),
        ("小组件", true, true),
        ("通知提醒", true, true),
        ("颜色图标", true, true),
        ("安全保护", true, true),
        ("无限数量", false, true),
        ("iCloud", false, true),
        ("数据导出", false, true),
        ("数据导入", false, true),
        ("优先客服", false, true)
    ]
    
    var body: some View {
        VStack(spacing: 0) {
            // 表头
            HStack {
                Text("功能")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                Text("免费版")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .frame(width: 60)
                
                Text("高级版")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.pink)
                    .frame(width: 60)
            }
            .padding(.bottom, 12)
            
            // 功能列表
            ForEach(Array(features.enumerated()), id: \.offset) { index, feature in
                HStack {
                    Text(feature.0)
                        .font(.body)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    Image(systemName: feature.1 ? "checkmark.circle.fill" : "xmark.circle")
                        .foregroundColor(feature.1 ? .green : .gray)
                        .frame(width: 60)
                    
                    Image(systemName: feature.2 ? "checkmark.circle.fill" : "xmark.circle")
                        .foregroundColor(feature.2 ? .pink : .gray)
                        .frame(width: 60)
                }
                .padding(.vertical, 8)
                
                if index < features.count - 1 {
                    Divider()
                }
            }
        }
    }
}



// MARK: - 预览
#Preview {
    PremiumView()
        .environmentObject(AppStateManager())
}
