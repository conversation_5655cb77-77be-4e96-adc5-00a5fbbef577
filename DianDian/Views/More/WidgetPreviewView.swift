//
//  WidgetPreviewView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import SwiftUI
import SwiftData

// MARK: - Widget 预览页面
struct WidgetPreviewView: View {
    @Environment(\.modelContext) private var context
    @Environment(\.dismiss) private var dismiss

    // SwiftData 查询 - 活跃事件
    @Query(filter: #Predicate<EventSwiftData> { event in
        !event.isArchived
    }, sort: \EventSwiftData.date)
    var activeEvents: [EventSwiftData]
    
    @State private var selectedWidgetSize: WidgetSize = .small
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 30) {
                    // 介绍
                    VStack(spacing: 16) {
                        Image(systemName: "rectangle.3.group")
                            .font(.system(size: 60))
                            .foregroundColor(.pink)
                        
                        Text("小组件")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("将重要的纪念日添加到桌面，随时查看")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    
                    // 尺寸选择
                    VStack(spacing: 16) {
                        Text("尺寸")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        HStack(spacing: 12) {
                            ForEach(WidgetSize.allCases, id: \.self) { size in
                                Button(action: {
                                    selectedWidgetSize = size
                                }) {
                                    Text(size.displayName)
                                        .font(.subheadline)
                                        .foregroundColor(selectedWidgetSize == size ? .white : .pink)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(
                                            RoundedRectangle(cornerRadius: 8)
                                                .fill(selectedWidgetSize == size ? Color.pink : Color.pink.opacity(0.1))
                                        )
                                }
                            }
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // Widget 预览
                    VStack(spacing: 16) {
                        Text("预览")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        WidgetPreview(size: selectedWidgetSize, events: getUpcomingEvents())
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                .padding()
            }
            .navigationTitle("小组件")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func getUpcomingEvents() -> [EventSwiftData] {
        return activeEvents
            .filter { $0.daysUntilNext >= 0 }
            .sorted { $0.daysUntilNext < $1.daysUntilNext }
            .prefix(10)
            .map { $0 }
    }
}

// MARK: - Widget 尺寸枚举
enum WidgetSize: String, CaseIterable {
    case small = "small"
    case medium = "medium"
    case large = "large"
    
    var displayName: String {
        switch self {
        case .small: return "小"
        case .medium: return "中"
        case .large: return "大"
        }
    }
    
    var size: CGSize {
        switch self {
        case .small: return CGSize(width: 155, height: 155)
        case .medium: return CGSize(width: 329, height: 155)
        case .large: return CGSize(width: 329, height: 345)
        }
    }
}

// MARK: - Widget 预览组件
struct WidgetPreview: View {
    let size: WidgetSize
    let events: [EventSwiftData]
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            
            switch size {
            case .small:
                SmallWidgetPreview(events: events)
            case .medium:
                MediumWidgetPreview(events: events)
            case .large:
                LargeWidgetPreview(events: events)
            }
        }
        .frame(width: size.size.width, height: size.size.height)
    }
}

// MARK: - 小尺寸 Widget
struct SmallWidgetPreview: View {
    let events: [EventSwiftData]

    private var nextEvent: EventSwiftData? {
        events.first
    }
    
    var body: some View {
        VStack(spacing: 8) {
            if let event = nextEvent {
                VStack(spacing: 4) {
                    Image(systemName: iconName(for: event.iconName))
                        .font(.title)
                        .foregroundColor(Color(hex: event.color) ?? .pink)
                    
                    Text(event.title)
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)
                    
                    Text("\(event.daysUntilNext)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.pink)
                    
                    Text(event.daysUntilNext == 0 ? "今天" : "天后")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            } else {
                VStack(spacing: 8) {
                    Image(systemName: "heart")
                        .font(.title)
                        .foregroundColor(.gray)
                    
                    Text("暂无纪念日")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
    }
}

// MARK: - 中尺寸 Widget
struct MediumWidgetPreview: View {
    let events: [EventSwiftData]
    
    var body: some View {
        HStack(spacing: 16) {
            // 左侧：下一个事件
            if let nextEvent = events.first {
                VStack(spacing: 8) {
                    Image(systemName: iconName(for: nextEvent.iconName))
                        .font(.title)
                        .foregroundColor(Color(hex: nextEvent.color) ?? .pink)
                    
                    Text(nextEvent.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)
                    
                    Text("\(nextEvent.daysUntilNext)")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.pink)
                    
                    Text(nextEvent.daysUntilNext == 0 ? "今天" : "天后")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
            }
            
            Divider()
            
            // 右侧：事件列表
            VStack(alignment: .leading, spacing: 6) {
                Text("即将到来")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                ForEach(events.prefix(3), id: \.id) { event in
                    HStack(spacing: 8) {
                        Image(systemName: iconName(for: event.iconName))
                            .font(.caption)
                            .foregroundColor(Color(hex: event.color) ?? .pink)
                            .frame(width: 12)
                        
                        Text(event.title)
                            .font(.caption)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Text("\(event.daysUntilNext)天")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                
                if events.isEmpty {
                    Text("暂无纪念日")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
    }
}

// MARK: - 大尺寸 Widget
struct LargeWidgetPreview: View {
    let events: [EventSwiftData]

    private var displayEvents: [EventSwiftData] {
        Array(events.prefix(5))
    }

    var body: some View {
        VStack(spacing: 16) {
            headerView
            eventListView
            Spacer()
        }
        .padding()
    }

    private var headerView: some View {
        HStack {
            Text("纪念日")
                .font(.headline)
                .fontWeight(.bold)
            Spacer()
            Image(systemName: "heart.fill")
                .foregroundColor(.pink)
        }
    }

    private var eventListView: some View {
        VStack(spacing: 12) {
            if events.isEmpty {
                emptyStateView
            } else {
                ForEach(displayEvents, id: \.id) { event in
                    eventRowView(event)

                    if event.id != displayEvents.last?.id {
                        Divider()
                    }
                }
            }
        }
    }

    private var emptyStateView: some View {
        VStack(spacing: 8) {
            Image(systemName: "heart")
                .font(.largeTitle)
                .foregroundColor(.gray)

            Text("暂无纪念日")
                .font(.subheadline)
                .foregroundColor(.secondary)

            Text("点击添加第一个纪念日")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private func eventRowView(_ event: EventSwiftData) -> some View {
        HStack(spacing: 12) {
            Image(systemName: iconName(for: event.iconName))
                .font(.title3)
                .foregroundColor(Color(hex: event.color) ?? .pink)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(event.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)

                Text(formatDate(event.date))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 2) {
                Text("\(event.daysUntilNext)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.pink)

                Text(event.daysUntilNext == 0 ? "今天" : "天后")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }

    private func formatDate(_ dateString: String) -> String {
        let isoFormatter = createISODateFormatter()
        if let date = isoFormatter.date(from: dateString) {
            let monthDayFormatter = createMonthDayFormatter()
            return monthDayFormatter.string(from: date)
        }
        return dateString
    }

    private func createISODateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        return formatter
    }

    private func createMonthDayFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "M月d日"
        formatter.locale = Locale.current
        return formatter
    }
}

// MARK: - 说明步骤组件
struct InstructionStep: View {
    let number: Int
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            ZStack {
                Circle()
                    .fill(Color.pink)
                    .frame(width: 24, height: 24)
                
                Text("\(number)")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - 辅助函数
private func iconName(for eventIcon: String) -> String {
    return eventIcon.isEmpty ? "heart.fill" : eventIcon
}

// MARK: - 预览
#Preview {
    WidgetPreviewView()
}
