import SwiftUI
import CloudKit

struct iCloudSyncView: View {
    @StateObject private var syncManager = CloudKitSyncManager()
    @Environment(\.dismiss) private var dismiss
    @State private var showingSyncAlert = false
    @State private var showingRestoreAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // iCloud状态卡片
                VStack(spacing: 16) {
                    // 状态图标和标题
                    HStack(spacing: 12) {
                        Image(systemName: accountStatusIcon)
                            .font(.title)
                            .foregroundColor(accountStatusColor)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("iCloud状态")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Text(syncManager.accountStatusDescription)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                    
                    // 同步状态详情（始终显示）
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("同步状态:")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Spacer()

                            HStack(spacing: 4) {
                                if case .syncing = syncManager.syncStatus {
                                    ProgressView()
                                        .scaleEffect(0.7)
                                }

                                Text(syncManager.syncStatusDescription)
                                    .font(.caption)
                                    .foregroundColor(syncStatusColor)
                            }
                        }

                        if let lastSyncDate = syncManager.lastSyncDate {
                            HStack {
                                Text("上次同步:")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Spacer()

                                Text(formatDate(lastSyncDate))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding(.top, 8)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // 操作按钮区域
                VStack(spacing: 16) {
                    // 同步数据到iCloud按钮
                    Button(action: {
                        syncToiCloud()
                    }) {
                        HStack {
                            Image(systemName: "icloud.and.arrow.up")
                                .font(.title2)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("同步数据到iCloud")
                                    .font(.body)
                                    .fontWeight(.medium)
                                
                                Text("将本地数据上传到iCloud")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            if case .syncing = syncManager.syncStatus {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding()
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color(.systemGray4), lineWidth: 1)
                        )
                    }
                    .disabled(!canPerformSync)
                    
                    // 从iCloud恢复数据按钮
                    Button(action: {
                        restoreFromiCloud()
                    }) {
                        HStack {
                            Image(systemName: "icloud.and.arrow.down")
                                .font(.title2)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("从iCloud恢复数据")
                                    .font(.body)
                                    .fontWeight(.medium)
                                
                                Text("从iCloud下载并恢复数据")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color(.systemGray4), lineWidth: 1)
                        )
                    }
                    .disabled(!canPerformSync)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("iCloud同步")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        dismiss()
                    }
                }
            }
        }
        .alert("同步结果", isPresented: $showingSyncAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .alert("恢复确认", isPresented: $showingRestoreAlert) {
            Button("取消", role: .cancel) { }
            Button("确认恢复", role: .destructive) {
                performRestore()
            }
        } message: {
            Text("从iCloud恢复数据将覆盖本地数据，此操作不可撤销。确定要继续吗？")
        }
        .onAppear {
            // 页面出现时检查账户状态
            Task {
                await syncManager.checkAccountStatus()
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var accountStatusIcon: String {
        switch syncManager.accountStatus {
        case .available:
            return "checkmark.icloud"
        case .noAccount:
            return "person.crop.circle.badge.xmark"
        case .restricted:
            return "exclamationmark.icloud"
        case .couldNotDetermine:
            return "questionmark.icloud"
        }
    }

    private var accountStatusColor: Color {
        switch syncManager.accountStatus {
        case .available:
            return .green
        case .noAccount:
            return .orange
        case .restricted:
            return .red
        case .couldNotDetermine:
            return .gray
        }
    }
    
    private var syncStatusColor: Color {
        switch syncManager.syncStatus {
        case .success:
            return .green
        case .failed:
            return .red
        case .syncing:
            return .blue
        case .idle, .disabled:
            return .secondary
        }
    }

    private var canPerformSync: Bool {
        syncManager.accountStatus == .available &&
        syncManager.syncStatus != .syncing
    }
    
    // MARK: - Methods
    
    private func syncToiCloud() {
        guard canPerformSync else { return }
        
        Task {
            // 直接执行手动同步，不依赖同步开关状态
            await syncManager.manualSync()

            // 显示结果
            await MainActor.run {
                switch syncManager.syncStatus {
                case .success:
                    alertMessage = "数据已成功同步到iCloud"
                case .failed(let error):
                    alertMessage = "同步失败: \(error.localizedDescription)"
                default:
                    alertMessage = "同步操作已完成"
                }
                showingSyncAlert = true
            }
        }
    }
    
    private func restoreFromiCloud() {
        showingRestoreAlert = true
    }
    
    private func performRestore() {
        Task {
            // 直接执行手动恢复，不依赖同步开关状态
            await syncManager.manualSync()

            // 显示结果
            await MainActor.run {
                switch syncManager.syncStatus {
                case .success:
                    alertMessage = "数据已从iCloud恢复"
                case .failed(let error):
                    alertMessage = "恢复失败: \(error.localizedDescription)"
                default:
                    alertMessage = "恢复操作已完成"
                }
                showingSyncAlert = true
            }
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        formatter.locale = Locale.current
        return formatter.string(from: date)
    }
}

#Preview {
    iCloudSyncView()
        .environmentObject(AppStateManager())
}
