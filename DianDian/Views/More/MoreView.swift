//
//  MoreView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import SwiftUI
import SwiftData

// MARK: - 更多页面视图
struct MoreView: View {
    @Environment(\.modelContext) private var context
    @EnvironmentObject var appState: AppStateManager
    @EnvironmentObject var securityManager: SecurityManager
    @StateObject private var notificationManager = NotificationManager()

    @State private var refreshID = UUID()

    @State private var showingAbout = false
    @State private var showingPremium = false
    @State private var showingWidgetPreview = false

    @State private var showingSecuritySettings = false
    @State private var showingDataExport = false
    @State private var showingDataImport = false
    @State private var showingICloudSync = false

    var body: some View {
        NavigationView {
            List {
                // 用户信息部分
                Section {
                    HStack(spacing: 16) {
                        Image(systemName: "person.circle.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.pink)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(appState.isPremiumUser ? "more.premium.user".localized : "more.free.user".localized)
                                .font(.headline)

                            Text("\("app.name".localized) \(appState.appVersion)")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if !appState.isPremiumUser {
                            Button("more.upgrade".localized) {
                                showingPremium = true
                            }
                            .buttonStyle(.borderedProminent)
                            .controlSize(.small)
                        }
                    }
                    .padding(.vertical, 8)
                }

                // 功能设置
                Section("more.settings".localized) {

                    SettingRow(
                        icon: "globe",
                        title: "more.language.settings".localized
                    ) {
                        openAppSettings()
                    }

                    SettingRow(
                        icon: "widget.small",
                        title: "more.widget.settings".localized
                    ) {
                        showingWidgetPreview = true
                    }

                    SettingRow(
                        icon: "lock.shield",
                        title: "安全"
                    ) {
                        showingSecuritySettings = true
                    }
                }

                // 数据同步
                Section("数据") {
                    
                    SettingRow(
                        icon: "icloud",
                        title: "iCloud"
                    ) {
                        showingICloudSync = true
                    }

                    SettingRow(
                        icon: "square.and.arrow.up",
                        title: "导出"
                    ) {
                        showingDataExport = true
                    }

                    SettingRow(
                        icon: "square.and.arrow.down",
                        title: "导入"
                    ) {
                        showingDataImport = true
                    }
                }

                // 帮助与支持
                Section("more.help.support".localized) {

                    SettingRow(
                        icon: "questionmark.circle",
                        title: "more.about.app".localized
                    ) {
                        showingAbout = true
                    }

                    SettingRow(
                        icon: "star",
                        title: "more.rate.app".localized
                    ) {
                        rateApp()
                    }

                    SettingRow(
                        icon: "envelope",
                        title: "more.contact.us".localized
                    ) {
                        contactUs()
                    }
                }
            }
            .navigationTitle("tab.more".localized)
            .navigationBarTitleDisplayMode(.large)
            .id(refreshID)
            .sheet(isPresented: $showingAbout) {
                AboutView()
            }
            .sheet(isPresented: $showingPremium) {
                PremiumView()
            }
            .sheet(isPresented: $showingWidgetPreview) {
                WidgetPreviewView()
            }

            .sheet(isPresented: $showingSecuritySettings) {
                SecuritySettingsView()
            }
            .sheet(isPresented: $showingDataExport) {
                DataExportView()
            }
            .sheet(isPresented: $showingDataImport) {
                DataImportView()
            }
            .sheet(isPresented: $showingICloudSync) {
                iCloudSyncView()
                    .environmentObject(appState)
            }

        }
    }
    
    private func rateApp() {
        // 跳转到 App Store 评价页面
        if let url = URL(string: "https://apps.apple.com/app/id1464903574?action=write-review") {
            UIApplication.shared.open(url)
        }
    }
    
    private func contactUs() {
        // 打开邮件应用
        if let url = URL(string: "mailto:<EMAIL>") {
            UIApplication.shared.open(url)
        }
    }

    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }

    private var languageDisplayName: String {
        switch appState.currentLanguage {
        case "zh-Hans": return "language.simplified.chinese".localized
        case "zh-Hant": return "language.traditional.chinese".localized
        case "en": return "language.english".localized
        default: return "language.simplified.chinese".localized
        }
    }

}

// MARK: - 设置行视图
struct SettingRow: View {
    let icon: String
    let title: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.pink)
                    .frame(width: 30)

                Text(title)
                    .font(.body)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(height: 30)
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 占位视图

// AboutView 已经在单独的文件中定义

// PremiumView 已经在单独的文件中定义

// MARK: - 数据导出视图
struct DataExportView: View {
    @Environment(\.modelContext) private var context
    @StateObject private var exportManager = DataImportExportManager.shared
    @Environment(\.dismiss) private var dismiss

    // SwiftData 查询 - 所有事件
    @Query var allEvents: [EventSwiftData]

    // SwiftData 查询 - 活跃事件
    @Query(filter: #Predicate<EventSwiftData> { event in
        !event.isArchived
    })
    var activeEvents: [EventSwiftData]

    // SwiftData 查询 - 归档事件
    @Query(filter: #Predicate<EventSwiftData> { event in
        event.isArchived
    })
    var archivedEvents: [EventSwiftData]

    @State private var showingShareSheet = false
    @State private var exportURL: URL?
    @State private var showingAlert = false
    @State private var alertMessage = ""

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 图标和标题
                VStack(spacing: 16) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.system(size: 60))
                        .foregroundColor(.pink)

                    Text("数据导出")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("将您的纪念日数据导出为备份文件")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                // 统计信息
                VStack(spacing: 12) {
                    StatRow(title: "活跃", value: "\(activeEvents.count) 个")
                    StatRow(title: "归档", value: "\(archivedEvents.count) 个")
                    StatRow(title: "总计", value: "\(allEvents.count) 个")

                    if let lastExport = exportManager.lastExportDate {
                        StatRow(title: "上次导出", value: formatDate(lastExport))
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)

                // 导出按钮
                VStack(spacing: 16) {
                    Button(action: {
                        exportData()
                    }) {
                        HStack {
                            if exportManager.isExporting {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "square.and.arrow.up")
                            }

                            Text(exportManager.isExporting ? "导出中..." : "导出")
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.pink)
                        .cornerRadius(25)
                    }
                    .disabled(exportManager.isExporting)

                    if exportManager.isExporting {
                        ProgressView(value: exportManager.exportProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .pink))
                    }


                }

                Spacer()
            }
            .padding()
            .navigationTitle("导出")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingShareSheet) {
                if let url = exportURL {
                    ShareSheet(items: [url])
                }
            }
            .alert("导出结果", isPresented: $showingAlert) {
                Button("确定") { }
            } message: {
                Text(alertMessage)
            }
        }
    }

    private func exportData() {
        Task {
            if let url = await exportManager.exportData() {
                await MainActor.run {
                    exportURL = url
                    showingShareSheet = true
                }
            } else {
                await MainActor.run {
                    alertMessage = "导出失败，请重试"
                    showingAlert = true
                }
            }
        }
    }



    private func formatDate(_ date: Date) -> String {
        let formatter = createDateTimeFormatter()
        return formatter.string(from: date)
    }

    private func createDateTimeFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale.current
        return formatter
    }
}

// MARK: - 数据导入视图
struct DataImportView: View {
    @Environment(\.modelContext) private var context
    @StateObject private var importManager = DataImportExportManager.shared
    @Environment(\.dismiss) private var dismiss

    // SwiftData 查询 - 所有事件
    @Query var allEvents: [EventSwiftData]

    @State private var showingFilePicker = false
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 图标和标题
                VStack(spacing: 16) {
                    Image(systemName: "square.and.arrow.down")
                        .font(.system(size: 60))
                        .foregroundColor(.pink)

                    Text("数据导入")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("从备份文件恢复您的纪念日数据")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                // 当前状态
                VStack(spacing: 12) {
                    StatRow(title: "当前纪念日", value: "\(allEvents.count) 个")

                    if let lastImport = importManager.lastImportDate {
                        StatRow(title: "上次导入", value: formatDate(lastImport))
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)

                // 导入按钮
                VStack(spacing: 16) {
                    Button(action: {
                        showingFilePicker = true
                    }) {
                        HStack {
                            if importManager.isImporting {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "square.and.arrow.down")
                            }

                            Text(importManager.isImporting ? "导入中..." : "选择备份文件")
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.pink)
                        .cornerRadius(25)
                    }
                    .disabled(importManager.isImporting)

                    if importManager.isImporting {
                        ProgressView(value: importManager.importProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .pink))
                    }
                }

                Spacer()
            }
            .padding()
            .navigationTitle("导入")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .fileImporter(
                isPresented: $showingFilePicker,
                allowedContentTypes: [.json],
                allowsMultipleSelection: false
            ) { result in
                handleFileImport(result)
            }
            .alert(alertTitle, isPresented: $showingAlert) {
                Button("确定") { }
            } message: {
                Text(alertMessage)
            }
        }
    }

    private func handleFileImport(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }

            Task {
                // 获取安全作用域访问权限
                let hasAccess = url.startAccessingSecurityScopedResource()
                defer {
                    if hasAccess {
                        url.stopAccessingSecurityScopedResource()
                    }
                }

                let result = await importManager.importData(from: url)

                await MainActor.run {
                    switch result {
                    case .success(let count):
                        alertTitle = "导入成功"
                        alertMessage = "成功导入 \(count) 个纪念日"
                        // SwiftData 会自动更新，不需要手动重新加载
                    case .failure(let error):
                        alertTitle = "导入失败"
                        alertMessage = error
                    }
                    showingAlert = true
                }
            }

        case .failure(let error):
            alertTitle = "文件选择失败"
            alertMessage = error.localizedDescription
            showingAlert = true
        }
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = createDateTimeFormatter()
        return formatter.string(from: date)
    }

    private func createDateTimeFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale.current
        return formatter
    }
}

// MARK: - 统计行视图
struct StatRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.body)
                .foregroundColor(.primary)
            Spacer()
            Text(value)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(.pink)
        }
    }
}

// MARK: - 分享表单
struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - 预览
#Preview {
    MoreView()
        .environmentObject(AppStateManager())
        .environmentObject(SecurityManager())
}
