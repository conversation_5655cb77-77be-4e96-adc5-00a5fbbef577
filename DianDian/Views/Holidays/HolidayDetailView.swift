//
//  HolidayDetailView.swift
//  DianDian
//
//  Created by Assistant on 2025/7/30.
//

import SwiftUI

// MARK: - 节日详情视图
struct HolidayDetailView: View {
    let holiday: HolidayModel
    @Environment(\.dismiss) private var dismiss
    
    private var holidayColor: Color {
        holiday.themeColor // 使用日历颜色作为主题色
    }
    
    private var daysTextColor: Color {
        let daysUntil = holiday.daysUntil
        if daysUntil == 0 {
            return holidayColor
        } else if daysUntil > 0 {
            return holidayColor
        } else {
            return .secondary
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 节日头部信息
                    VStack(spacing: 16) {
                        // 图标和标题
                        VStack(spacing: 12) {
                            ZStack {
                                Circle()
                                    .fill(holidayColor.opacity(0.2))
                                    .frame(width: 80, height: 80)
                                
                                Image(systemName: holiday.type.icon)
                                    .font(.system(size: 32))
                                    .foregroundColor(holidayColor)
                            }
                            
                            Text(holiday.name)
                                .font(.title2)
                                .fontWeight(.bold)
                                .multilineTextAlignment(.center)
                        }
                        
                        // 剩余天数或状态
                        VStack(spacing: 4) {
                            let daysUntil = holiday.daysUntil
                            let isToday = daysUntil == 0

                            if isToday {
                                Text("event.detail.today".localized)
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.orange)
                            } else if daysUntil > 0 {
                                Text("days.remaining".localized(daysUntil))
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.primary)
                            } else {
                                Text("days.passed".localized(abs(daysUntil)))
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.red)
                            }

                            Text(formatHolidayDate(holiday.startDateObject ?? Date()))
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.top, 20)
                    
                    // 详细信息卡片
                    VStack(spacing: 16) {
                        // 基本信息
                        EventInfoCard(title: "section.basic.info".localized) {
                            EventInfoRow(
                                label: "holiday.date".localized,
                                value: formatHolidayDate(holiday.startDateObject ?? Date()),
                                icon: "calendar",
                                iconColor: holidayColor
                            )
                            EventInfoRow(
                                label: "holiday.type".localized,
                                value: holiday.calendarTitle ?? formatHolidayDate(),
                                icon: "calendar.badge.plus",
                                iconColor: holidayColor
                            )
                        }

                        // 备注信息
                        if !holiday.description.isEmpty {
                            EventInfoCard(title: "holiday.description".localized) {
                                Text(holiday.description)
                                    .font(.body)
                                    .foregroundColor(.primary)
                                    .multilineTextAlignment(.leading)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .padding(.vertical, 8)
                            }
                        }
                    }
                    .standardHorizontalPadding()
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle(holiday.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("button.close".localized) {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    private func formatHolidayDate(_ date: Date) -> String {
        let formatter = createFullDateFormatter()
        return formatter.string(from: date)
    }

    private func formatHolidayDate() -> String {
        guard let date = holiday.startDateObject else {
            return holiday.startDate
        }

        let formatter = createFullDateFormatter()
        return formatter.string(from: date)
    }

    private func createFullDateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale.current
        return formatter
    }
}

// MARK: - 预览
#Preview {
    let sampleHoliday = HolidayModel(
        id: "sample",
        name: "春节",
        startDate: "2024-02-10",
        endDate: "2024-02-10",
        type: .traditional,
        description: "中国传统新年",
        isOfficial: true,
        calendarColor: "#FF0000",
        calendarTitle: "中国节日",
        isBirthday: false
    )
    HolidayDetailView(holiday: sampleHoliday)
}
