//
//  HolidayListView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import SwiftUI
import EventKit

// MARK: - 节日列表视图
struct HolidayListView: View {
    @StateObject private var holidayService = HolidayDataService()
    @State private var searchText = ""
    private let currentYear = Calendar.current.component(.year, from: Date())
    @State private var selectedHoliday: HolidayModel?
    @State private var showingHolidayDetail = false
    @State private var hasLoadedInitialData = false

    private var filteredHolidays: [HolidayModel] {
        var holidays = holidayService.holidays

        // 按搜索文本筛选
        if !searchText.isEmpty {
            holidays = holidays.filter { holiday in
                holiday.name.localizedCaseInsensitiveContains(searchText) ||
                holiday.description.localizedCaseInsensitiveContains(searchText)
            }
        }

        return holidays
    }

    private var groupedHolidays: [Date: [HolidayModel]] {
        Dictionary(grouping: filteredHolidays) { holiday in
            guard let date = holiday.startDateObject else { return Date.distantPast }
            return Calendar.current.startOfDay(for: date)
        }
    }

    private var sortedHolidayGroups: [(Date, [HolidayModel])] {
        let today = Calendar.current.startOfDay(for: Date())
        let groups = groupedHolidays.map { (date, holidays) in
            (date, holidays.sorted { $0.startDateObject ?? Date.distantPast < $1.startDateObject ?? Date.distantPast })
        }

        // 分离今天及未来的节日和已过的节日
        let futureGroups = groups.filter { $0.0 >= today }.sorted { $0.0 < $1.0 }
        let pastGroups = groups.filter { $0.0 < today }.sorted { $0.0 > $1.0 } // 已过的节日按时间倒序

        // 先显示今天及未来的节日，再显示已过的节日
        return futureGroups + pastGroups
    }

    var body: some View {
        NavigationView {
            // 检查日历权限状态
            if !holidayService.calendarAccessGranted {
                // 显示未授权界面
                CalendarPermissionView(
                    onRequestPermission: {
                        holidayService.requestCalendarPermission()
                    }
                )
                .background(Color(.systemGroupedBackground))
                .navigationTitle("节日")
                .navigationBarTitleDisplayMode(.large)
            } else {
                // 有权限时显示正常界面
                holidayContentView
                    .background(Color(.systemGroupedBackground))
                    .navigationTitle("节日")
                    .navigationBarTitleDisplayMode(.large)
            }
        }
        .onAppear {
            if holidayService.calendarAccessGranted && !hasLoadedInitialData {
                holidayService.loadHolidays(for: currentYear)
                hasLoadedInitialData = true
            }
        }
        .sheet(item: $selectedHoliday) { holiday in
            HolidayDetailView(holiday: holiday)
        }
    }

    // MARK: - 节日内容视图
    private var holidayContentView: some View {
        VStack(spacing: 0) {
            // 搜索栏
            EnhancedSearchBar(
                text: $searchText,
                placeholder: "search.holidays.placeholder".localized
            )

            // 主内容
            if holidayService.isLoading {
                LoadingView()
            } else if filteredHolidays.isEmpty {
                EmptyHolidaysView(
                    isSearching: !searchText.isEmpty,
                    onRefresh: {
                        holidayService.loadHolidays(for: currentYear)
                    }
                )
            } else {
                List {
                    // 今日节日
                    let todayHolidays = holidayService.getTodayHolidays()
                    if !todayHolidays.isEmpty {
                        Section("今日节日") {
                            ForEach(todayHolidays, id: \.id) { holiday in
                                ModernHolidayRowView(holiday: holiday, isToday: true)
                                    .listRowInsets(LayoutConstants.standardListRowInsets)
                                    .listRowSeparator(.hidden)
                                    .listRowBackground(Color.clear)
                                    .onTapGesture {
                                        selectedHoliday = holiday
                                        showingHolidayDetail = true
                                    }
                            }
                        }
                    }

                    // 即将到来的节日
                    let upcomingHolidays = holidayService.getUpcomingHolidays()
                    if !upcomingHolidays.isEmpty && searchText.isEmpty {
                        Section("即将到来") {
                            ForEach(upcomingHolidays, id: \.id) { holiday in
                                ModernHolidayRowView(holiday: holiday)
                                    .listRowInsets(LayoutConstants.standardListRowInsets)
                                    .listRowSeparator(.hidden)
                                    .listRowBackground(Color.clear)
                                    .onTapGesture {
                                        selectedHoliday = holiday
                                        showingHolidayDetail = true
                                    }
                            }
                        }
                    }

                    // 按日期分组的节日
                    if searchText.isEmpty {
                        ForEach(sortedHolidayGroups, id: \.0) { date, holidays in
                            Section(header: HolidayDateHeaderView(date: date)) {
                                ForEach(holidays, id: \.id) { holiday in
                                    ModernHolidayRowView(holiday: holiday)
                                        .listRowInsets(LayoutConstants.standardListRowInsets)
                                        .listRowSeparator(.hidden)
                                        .listRowBackground(Color.clear)
                                        .onTapGesture {
                                            selectedHoliday = holiday
                                            showingHolidayDetail = true
                                        }
                                }
                            }
                        }
                    } else {
                        // 搜索结果
                        Section("搜索结果") {
                            ForEach(filteredHolidays, id: \.id) { holiday in
                                ModernHolidayRowView(holiday: holiday)
                                    .listRowInsets(LayoutConstants.standardListRowInsets)
                                    .listRowSeparator(.hidden)
                                    .listRowBackground(Color.clear)
                                    .onTapGesture {
                                        selectedHoliday = holiday
                                        showingHolidayDetail = true
                                    }
                            }
                        }
                    }
                }
                .listStyle(PlainListStyle())
                .refreshable {
                    await refreshHolidays()
                }
            }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    holidayService.loadHolidays(for: currentYear)
                }) {
                    Image(systemName: "arrow.clockwise")
                }
            }
        }
    }

    // MARK: - 私有方法
    private func refreshHolidays() async {
        holidayService.loadHolidays(for: currentYear)
        // 给用户一点时间看到刷新动画
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
    }
}
// MARK: - 节日日期标题视图
struct HolidayDateHeaderView: View {
    let date: Date

    private var isPastDate: Bool {
        let today = Calendar.current.startOfDay(for: Date())
        return date < today
    }

    private var dateText: String {
        let formatter = createFullDateFormatter()
        return formatter.string(from: date)
    }

    private func createFullDateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale.current
        return formatter
    }

    var body: some View {
        HStack(spacing: 6) {
            if isPastDate {
                Image(systemName: "clock.arrow.circlepath")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.secondary)
            }

            Text(dateText)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(isPastDate ? .secondary : .primary)

            Spacer()
        }
        // .padding(.horizontal, 20)
        // .padding(.vertical, 8)
    }
}

// MARK: - 加载视图
struct LoadingView: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)

            Text("加载节日数据...")
                .font(.body)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - 空状态视图
struct EmptyHolidaysView: View {
    let isSearching: Bool
    let onRefresh: () -> Void

    var body: some View {
        VStack(spacing: 30) {
            Spacer()

            Image(systemName: isSearching ? "magnifyingglass" : "calendar.circle")
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            VStack(spacing: 10) {
                Text(isSearching ? "没有找到相关节日" : "暂无节日数据")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text(isSearching ? "尝试使用其他关键词搜索" : "点击刷新加载节日数据")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }

            if !isSearching {
                Button("刷新") {
                    onRefresh()
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(width: 120, height: 44)
                .background(Color.blue)
                .cornerRadius(22)
            }

            Spacer()
        }
    }
}

// MARK: - 现代化节日行视图
struct ModernHolidayRowView: View {
    let holiday: HolidayModel
    let isToday: Bool

    init(holiday: HolidayModel, isToday: Bool = false) {
        self.holiday = holiday
        self.isToday = isToday
    }

    private var daysText: String {
        let days = holiday.daysUntil
        if days == 0 {
            return "今天"
        } else if days > 0 {
            return "\(days)天"
        } else {
            return "\(abs(days))天"
        }
    }

    private var daysStatus: String {
        let days = holiday.daysUntil
        if days == 0 {
            return "今天"
        } else if days > 0 {
            return "还有"
        } else {
            return "已过"
        }
    }

    private var isPastHoliday: Bool {
        holiday.daysUntil < 0
    }

    private var textOpacity: Double {
        isPastHoliday ? 0.6 : 1.0
    }

    private var iconColor: Color {
        if isToday {
            return holiday.themeColor
        } else if isPastHoliday {
            return holiday.themeColor.opacity(0.5)
        } else {
            return holiday.themeColor
        }
    }

    private var rightTextColor: Color {
        if isToday {
            return holiday.themeColor
        } else if isPastHoliday {
            return .secondary
        } else {
            return holiday.themeColor
        }
    }

    private func formatHolidayDate() -> String {
        guard let date = holiday.startDateObject else {
            return holiday.startDate
        }

        let formatter = createFullDateFormatter()
        return formatter.string(from: date)
    }

    private func createFullDateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale.current
        return formatter
    }

    var body: some View {
        HStack(spacing: 16) {
            // 左侧圆形图标
            ZStack {
                Circle()
                    .fill(iconColor)
                    .frame(width: 44, height: 44)

                // 根据是否为生日事件显示不同图标
                Image(systemName: holiday.isBirthday ? "gift.fill" : "star.fill")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.white)

                // 为生日事件添加额外的装饰
                if holiday.isBirthday {
                    Circle()
                        .stroke(Color.yellow, lineWidth: 4)
                        .frame(width: 44, height: 44)
                }
            }

            // 中间内容
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 6) {
                    Text(holiday.name)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                        .opacity(textOpacity)
                        .lineLimit(1)

                    if isPastHoliday {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }

                    // 生日事件标识
                    if holiday.isBirthday {
                        Image(systemName: "gift.fill")
                            .font(.system(size: 14))
                            .foregroundColor(.orange)
                    }
                }

                Text(holiday.calendarTitle ?? formatHolidayDate())
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .opacity(textOpacity)
            }

            Spacer()

            // 右侧内容
            VStack(alignment: .trailing, spacing: 4) {
                Text(daysText)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(rightTextColor)
                    .opacity(textOpacity)

                Text(daysStatus)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
                    .opacity(textOpacity)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color("MainBgColor"))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray5), lineWidth: 0.5)
                )
        )
        .contentShape(Rectangle())
    }
}

// MARK: - 预览
#Preview {
    HolidayListView()
}

#Preview("节日行视图") {
    VStack(spacing: 8) {
        ModernHolidayRowView(
            holiday: HolidayModel(
                id: "1",
                name: "春节",
                startDate: "2024-02-10",
                endDate: "2024-02-17",
                type: .traditional,
                description: "中国传统新年",
                isOfficial: true,
                calendarColor: "#FF0000",
                calendarTitle: "中国节日",
                isBirthday: false
            ),
            isToday: true
        )

        ModernHolidayRowView(
            holiday: HolidayModel(
                id: "2",
                name: "情人节",
                startDate: "2024-02-14",
                endDate: "2024-02-14",
                type: .western,
                description: "西方传统情人节",
                isOfficial: false,
                calendarColor: "#FF69B4",
                calendarTitle: "西方节日",
                isBirthday: false
            )
        )

        ModernHolidayRowView(
            holiday: HolidayModel(
                id: "3",
                name: "国际妇女节",
                startDate: "2024-03-08",
                endDate: "2024-03-08",
                type: .international,
                description: "国际劳动妇女节",
                isOfficial: false,
                calendarColor: "#9C27B0",
                calendarTitle: "国际节日",
                isBirthday: false
            )
        )

        // 添加生日事件示例
        ModernHolidayRowView(
            holiday: HolidayModel(
                id: "4",
                name: "张三的生日",
                startDate: "2024-03-15",
                endDate: "2024-03-15",
                type: .calendar,
                description: "生日快乐！",
                isOfficial: false,
                calendarColor: "#FF9500",
                calendarTitle: "生日日历",
                isBirthday: true
            )
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}

// MARK: - 日历权限视图
struct CalendarPermissionView: View {
    let onRequestPermission: () -> Void

    @State private var permissionStatus: EKAuthorizationStatus = .notDetermined

    var buttonText: String {
        switch permissionStatus {
        case .notDetermined:
            return "授权访问日历"
        case .denied, .restricted:
            return "前往设置开启"
        default:
            return "授权访问日历"
        }
    }

    var buttonIcon: String {
        switch permissionStatus {
        case .notDetermined:
            return "calendar"
        case .denied, .restricted:
            return "gear"
        default:
            return "calendar"
        }
    }

    var body: some View {
        VStack(spacing: 30) {
            Spacer()

            // 图标
            Image(systemName: "calendar.badge.exclamationmark")
                .font(.system(size: 80))
                .foregroundColor(.orange)

            // 标题和描述
            VStack(spacing: 16) {
                Text("需要日历权限")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                VStack(spacing: 8) {
                    Text("节日信息来自您的日历")
                        .font(.body)
                        .foregroundColor(.secondary)

                    if permissionStatus == .denied || permissionStatus == .restricted {
                        Text("请在设置中开启日历权限")
                            .font(.body)
                            .foregroundColor(.secondary)
                    } else {
                        Text("请授权访问日历以显示节日信息")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                }
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            }

            // 授权按钮
            Button(action: onRequestPermission) {
                HStack {
                    Image(systemName: buttonIcon)
                    Text(buttonText)
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(width: 200, height: 50)
                .background(Color.blue)
                .cornerRadius(25)
            }

            // 说明文字
            Text("授权后，应用将读取您日历中的节日信息并显示")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
        .onAppear {
            permissionStatus = EKEventStore.authorizationStatus(for: .event)
        }
    }
}
