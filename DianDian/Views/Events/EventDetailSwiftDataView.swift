//
//  EventDetailSwiftDataView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/29.
//

import SwiftUI
import SwiftData

struct EventDetailSwiftDataView: View {
    @Environment(\.modelContext) private var context
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var notificationManager: NotificationManager

    let event: EventSwiftData

    @State private var showingDeleteAlert = false
    @State private var showingEditEvent = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 事件头部信息
                    VStack(spacing: 16) {
                        // 图标和标题
                        VStack(spacing: 12) {
                            ZStack {
                                Circle()
                                    .fill(event.eventColor.opacity(0.2))
                                    .frame(width: 80, height: 80)
                                
                                Image(systemName: event.iconName.isEmpty ? "heart.fill" : event.iconName)
                                    .font(.system(size: 32))
                                    .foregroundColor(event.eventColor)
                            }
                            
                            Text(event.title)
                                .font(.title2)
                                .fontWeight(.bold)
                                .multilineTextAlignment(.center)
                        }
                        
                        // 剩余天数
                        VStack(spacing: 4) {
                            Text(event.daysUntilNextText)
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(daysTextColor)
                            
                            if let nextDate = event.nextOccurrenceDate {
                                Text(formatNextDate(nextDate))
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding(.top, 20)
                    
                    // 详细信息卡片
                    VStack(spacing: 16) {
                        // 基本信息
                        EventInfoCard(title: "section.basic.info".localized) {
                            EventInfoRow(
                                label: "detail.original.date".localized,
                                value: formatEventDate(),
                                icon: "calendar",
                                iconColor: eventColor
                            )
                            EventInfoRow(
                                label: "event.repeat".localized,
                                value: event.cycleTypeEnum.displayName,
                                icon: "repeat",
                                iconColor: eventColor
                            )
                            if event.isLunar {
                                EventInfoRow(
                                    label: "event.calendar.type".localized,
                                    value: "calendar.lunar".localized,
                                    icon: "moon.fill",
                                    iconColor: eventColor
                                )
                            }
                        }
                        
                        // 通知设置
                        if event.isNotificationEnabled {
                            EventInfoCard(title: "section.notification.settings".localized) {
                                EventInfoRow(
                                    label: "event.notification.time".localized,
                                    value: event.notificationTime,
                                    icon: "clock",
                                    iconColor: eventColor
                                )
                                EventInfoRow(
                                    label: "event.advance.notification".localized,
                                    value: event.advanceNotificationTypeEnum.displayName,
                                    icon: "alarm",
                                    iconColor: eventColor
                                )
                            }
                        }

                        // 备注信息
                        if !event.note.isEmpty {
                            EventInfoCard(title: "event.note".localized) {
                                HStack {
                                    Text(event.note)
                                        .font(.body)
                                        .foregroundColor(.primary)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                        .fixedSize(horizontal: false, vertical: true)
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                    
                    // 归档和删除按钮
                    HStack(spacing: 12) {
                        // 归档按钮
                        Button(action: {
                            archiveEvent()
                        }) {
                            HStack {
                                Image(systemName: event.isArchived ? "tray.and.arrow.up" : "archivebox")
                                Text(event.isArchived ? "event.unarchive.title".localized : "event.archive.title".localized)
                            }
                            .font(.headline)
                            .foregroundColor(.orange)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(Color.orange.opacity(0.1))
                            .cornerRadius(12)
                        }

                        // 删除按钮
                        Button(action: {
                            showingDeleteAlert = true
                        }) {
                            HStack {
                                Image(systemName: "trash")
                                Text("event.delete.title".localized)
                            }
                            .font(.headline)
                            .foregroundColor(.red)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(Color.red.opacity(0.1))
                            .cornerRadius(12)
                        }
                    }
                    .padding(.top, 8)
                    .padding(.horizontal, 20)
                }
                .padding(.vertical)
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("button.close".localized) {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("button.edit".localized) {
                        showingEditEvent = true
                    }
                }
            }
            .alert("删除事件", isPresented: $showingDeleteAlert) {
                Button("删除", role: .destructive) {
                    deleteEvent()
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("确定要删除这个纪念日吗？此操作无法撤销。")
            }
            .sheet(isPresented: $showingEditEvent) {
                EventEditSwiftDataView(event: event)
            }
        }
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - 私有方法

    private var eventColor: Color {
        Color(hex: event.color) ?? .blue
    }

    private var daysTextColor: Color {
        let days = event.daysUntilNext
        if days == 0 {
            return .red
        } else if days <= 7 {
            return .orange
        } else {
            return .primary
        }
    }
    
    private func formatEventDate() -> String {
        guard let date = event.eventDate else { return NSLocalizedString("未知日期", comment: "Unknown date") }

        if event.isLunar {
            // 农历格式化功能暂时不可用
            let fullDateFormatter = createFullDateFormatter()
            return "\(fullDateFormatter.string(from: date)) (农历)"
        } else {
            let fullDateFormatter = createFullDateFormatter()
            return fullDateFormatter.string(from: date)
        }
    }
    
    private func formatNextDate(_ date: Date) -> String {
        if event.isLunar {
            // 农历格式化功能暂时不可用
            let fullDateFormatter = createFullDateFormatter()
            let solarString = fullDateFormatter.string(from: date)
            return "\(solarString) (农历)"
        } else {
            let fullDateFormatter = createFullDateFormatter()
            return fullDateFormatter.string(from: date)
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = createDateTimeFormatter()
        return formatter.string(from: date)
    }

    private func createFullDateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.locale = Locale.current
        return formatter
    }

    private func createDateTimeFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale.current
        return formatter
    }



    private func deleteEvent() {
        withAnimation {
            // 先取消通知
            notificationManager.cancelNotification(for: event)

            context.delete(event)
            do {
                try context.save()
                print("✅ 事件删除成功")
                dismiss()
            } catch {
                print("❌ 删除事件失败: \(error)")
            }
        }
    }

    private func archiveEvent() {
        withAnimation {
            let _ = event.isArchived
            event.isArchived.toggle()
            event.updatedAt = Date()

            do {
                try context.save()
                print("✅ 事件归档状态更新成功: \(event.isArchived ? "已归档" : "已取消归档")")

                // 处理通知
                if event.isArchived {
                    // 归档时取消通知
                    notificationManager.cancelNotification(for: event)
                    print("🗑️ 已取消归档事件的通知")
                } else {
                    // 取消归档时重新添加通知（如果启用了通知）
                    if event.isNotificationEnabled {
                        notificationManager.scheduleNotification(for: event)
                        print("🔔 已重新调度取消归档事件的通知")
                    }
                }

                // 归档或取消归档操作完成后，自动关闭详情页面
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    dismiss()
                }
            } catch {
                print("❌ 更新事件归档状态失败: \(error)")
            }
        }
    }
}

// MARK: - 信息卡片组件
struct EventInfoCard<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .foregroundColor(.primary)
            
            content
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color("MainBgColor"))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray5), lineWidth: 0.5)
                )
        )
    }
}

// MARK: - 信息行组件
struct EventInfoRow: View {
    let label: String
    let value: String
    let icon: String?
    let iconColor: Color

    init(label: String, value: String, icon: String? = nil, iconColor: Color = .blue) {
        self.label = label
        self.value = value
        self.icon = icon
        self.iconColor = iconColor
    }

    var body: some View {
        HStack {
            if let icon = icon {
                Image(systemName: icon)
                    .foregroundColor(iconColor)
                    .frame(width: 20)
                    .font(.system(size: 16, weight: .medium))
            }

            Text(label)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .foregroundColor(.primary)
        }
        .font(.body)
    }
}

#Preview {
    let sampleEvent = EventSwiftData()
    sampleEvent.title = "生日"
    sampleEvent.date = "2025-08-15"
    sampleEvent.note = "记得准备礼物"
    sampleEvent.color = "blue"
    sampleEvent.iconName = "heart.fill"
    sampleEvent.isNotificationEnabled = true
    
    return EventDetailSwiftDataView(event: sampleEvent)
}
