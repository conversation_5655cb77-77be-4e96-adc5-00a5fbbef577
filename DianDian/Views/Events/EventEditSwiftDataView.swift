//
//  EventEditSwiftDataView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/29.
//

import SwiftUI
import SwiftData

struct EventEditSwiftDataView: View {
    @Environment(\.modelContext) private var context
    @Environment(\.dismiss) private var dismiss

    @StateObject private var notificationManager = NotificationManager()

    let event: EventSwiftData?
    
    @State private var title: String = ""
    @State private var selectedDate: Date = Date()
    @State private var selectedCycleType: EventCycleType = .never
    @State private var notificationTime: Date = Date()
    @State private var isLunar: Bool = false
    @State private var note: String = ""
    @State private var selectedColor: String = "FF3B30"
    @State private var selectedIcon: String = "gift.fill"
    @State private var isNotificationEnabled: Bool = true
    @State private var advanceNotificationType: EventAdvanceNotificationType = .onTime

    @State private var showingColorPicker = false
    @State private var showingIconPicker = false
    @State private var showingPermissionAlert = false
    
    private var isEditing: Bool {
        event != nil
    }
    
    private var navigationTitle: String {
        isEditing ? "编辑" : "添加"
    }

    private var canSave: Bool {
        !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    private var eventColor: Color {
        Color(hex: selectedColor) ?? .pink
    }

    private var iconSystemName: String {
        selectedIcon.isEmpty ? "heart.fill" : selectedIcon
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("详情") {
                    HStack {
                        Image(systemName: "highlighter")
                            .foregroundColor(eventColor)
                            .frame(width: 20)
                        TextField("标题", text: $title)
                    }

                    LunarDatePicker(selectedDate: $selectedDate, isLunar: $isLunar, eventColor: eventColor)

                    HStack {
                        Image(systemName: "repeat")
                            .foregroundColor(eventColor)
                            .frame(width: 20)
                        Picker("循环", selection: $selectedCycleType) {
                            if isLunar {
                                // 农历事件只允许"永不"和"每年"
                                Text(EventCycleType.never.displayName).tag(EventCycleType.never)
                                Text(EventCycleType.yearly.displayName).tag(EventCycleType.yearly)
                            } else {
                                // 公历事件允许所有类型
                                ForEach(EventCycleType.allCases, id: \.self) { type in
                                    Text(type.displayName).tag(type)
                                }
                            }
                        }
                    }

                    // 只有在中文环境下才显示农历选项
                    if shouldShowLunarOption {
                        HStack {
                            Image(systemName: "moon.fill")
                                .foregroundColor(eventColor)
                                .frame(width: 20)
                            Toggle("农历", isOn: $isLunar)
                        }
                        .onChange(of: isLunar) { _, newValue in
                            if newValue {
                                // 切换到农历时，如果当前类型不是永不或每年，则设置为每年循环
                                if selectedCycleType != .never && selectedCycleType != .yearly {
                                    selectedCycleType = .yearly
                                }
                            }
                        }
                    }
                }
                
                Section("通知") {
                    HStack {
                        Image(systemName: "bell")
                            .foregroundColor(eventColor)
                            .frame(width: 20)
                        Toggle("通知", isOn: $isNotificationEnabled)
                    }
                    .onChange(of: isNotificationEnabled) { _, newValue in
                        if newValue {
                            // 检查通知权限
                            if notificationManager.authorizationStatus != .authorized {
                                // 权限未授权，显示提示并重置开关
                                isNotificationEnabled = false
                                showingPermissionAlert = true
                                return
                            }

                            // 启用通知时，如果当前是"不提醒"，则设置为"准时提醒"
                            if advanceNotificationType == .none {
                                advanceNotificationType = .onTime
                            }
                        } else {
                            // 关闭通知时，设置为"不提醒"
                            advanceNotificationType = .none
                        }
                    }

                    if isNotificationEnabled {
                        HStack {
                            Image(systemName: "clock")
                                .foregroundColor(eventColor)
                                .frame(width: 20)
                            DatePicker("时间", selection: $notificationTime, displayedComponents: .hourAndMinute)
                        }

                        HStack {
                            Image(systemName: "alarm")
                                .foregroundColor(eventColor)
                                .frame(width: 20)
                            Picker("提前", selection: $advanceNotificationType) {
                                ForEach(EventAdvanceNotificationType.enabledNotificationCases, id: \.self) { type in
                                    Text(type.displayName).tag(type)
                                }
                            }
                        }
                    }
                }

                Section("外观") {
                    Button(action: {
                        showingColorPicker = true
                    }) {
                        HStack {
                            Image(systemName: "paintpalette")
                                .foregroundColor(eventColor)
                                .frame(width: 20)
                            Text("颜色")
                                .foregroundColor(.primary)
                            Spacer()
                            Circle()
                                .fill(eventColor)
                                .frame(width: 24, height: 24)
                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }

                    Button(action: {
                        showingIconPicker = true
                    }) {
                        HStack {
                            Image(systemName: "star")
                                .foregroundColor(eventColor)
                                .frame(width: 20)
                            Text("图标")
                                .foregroundColor(.primary)
                            Spacer()
                            Image(systemName: iconSystemName)
                                .foregroundColor(eventColor)
                                .font(.title3)
                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }
                }

                Section("备注") {
                    HStack {
                        TextField("备注（可选）", text: $note, axis: .vertical)
                            .lineLimit(3...6)
                    }
                }
            }
            .scrollContentBackground(.hidden)
            .background(Color(.systemGroupedBackground))
            .navigationTitle(navigationTitle)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveEvent()
                    }
                    .disabled(!canSave)
                    .fontWeight(.semibold)
                }
            }
            .sheet(isPresented: $showingColorPicker) {
                NavigationView {
                    ColorPickerView(selectedColor: $selectedColor)
                        .navigationTitle("选择颜色")
                        .navigationBarTitleDisplayMode(.inline)
                        .toolbar {
                            ToolbarItem(placement: .navigationBarTrailing) {
                                Button("完成") {
                                    showingColorPicker = false
                                }
                            }
                        }
                }
                .presentationDetents([.medium, .large])
            }
            .sheet(isPresented: $showingIconPicker) {
                NavigationView {
                    IconPickerView(selectedIcon: $selectedIcon)
                        .navigationTitle("选择图标")
                        .navigationBarTitleDisplayMode(.inline)
                        .toolbar {
                            ToolbarItem(placement: .navigationBarTrailing) {
                                Button("完成") {
                                    showingIconPicker = false
                                }
                            }
                        }
                }
                .presentationDetents([.medium, .large])
            }
        }
        .onAppear {
            // 先检查通知权限状态，然后加载事件数据
            notificationManager.checkAuthorizationStatus()

            // 延迟一点时间确保权限状态已更新
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                loadEventData()
            }
        }
        .onChange(of: notificationManager.authorizationStatus) { _, _ in
            // 当通知权限状态变化时，重新加载事件数据
            loadEventData()
        }
        .alert("需要通知权限", isPresented: $showingPermissionAlert) {
            Button("去设置") {
                notificationManager.openSettings()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("要启用通知功能，请在设置中允许DianDian发送通知。")
        }
    }
    
    // MARK: - 私有方法
    
    private func loadEventData() {
        guard let event = event else {
            // 新建事件时，根据通知权限状态设置默认值
            if notificationManager.authorizationStatus != .authorized {
                isNotificationEnabled = false
                advanceNotificationType = .none
            }
            print("📝 新建事件 - 通知启用: \(isNotificationEnabled)")
            return
        }

        title = event.title
        selectedDate = event.eventDate ?? Date()
        selectedCycleType = event.cycleTypeEnum
        notificationTime = event.notificationTimeDate
        isLunar = event.isLunar
        note = event.note
        selectedColor = event.color
        selectedIcon = event.iconName

        print("📝 加载事件数据: \(event.title)")
        print("🔔 事件通知状态: \(event.isNotificationEnabled)")
        print("🔔 通知权限状态: \(notificationManager.authorizationStatus)")

        // 编辑现有事件时，如果权限被撤销，强制关闭通知
        if notificationManager.authorizationStatus != .authorized {
            isNotificationEnabled = false
            advanceNotificationType = .none
            print("⚠️ 通知权限未授权，强制关闭通知")
        } else {
            isNotificationEnabled = event.isNotificationEnabled
            advanceNotificationType = event.advanceNotificationTypeEnum
            print("✅ 通知权限已授权，加载事件通知设置: \(isNotificationEnabled)")
        }
    }
    
    private func saveEvent() {
        let cleanedTitle = title.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !cleanedTitle.isEmpty else { return }
        
        let dateFormatter = createISODateFormatter()
        let timeFormatter = createHourMinuteFormatter()
        
        if let existingEvent = event {
            // 更新现有事件
            existingEvent.title = cleanedTitle
            existingEvent.date = dateFormatter.string(from: selectedDate)
            existingEvent.cycleType = selectedCycleType.rawValue
            existingEvent.notificationTime = timeFormatter.string(from: notificationTime)
            existingEvent.isLunar = isLunar
            existingEvent.note = note.trimmingCharacters(in: .whitespacesAndNewlines)
            existingEvent.color = selectedColor
            existingEvent.iconName = selectedIcon
            existingEvent.isNotificationEnabled = isNotificationEnabled
            existingEvent.advanceNotificationType = advanceNotificationType.rawValue
            existingEvent.updatedAt = Date()

            print("✅ 更新事件: \(existingEvent.title)")
            print("📝 通知时间: \(existingEvent.notificationTime)")
            print("🔔 通知启用: \(existingEvent.isNotificationEnabled)")

            // 先保存到数据库
            do {
                try context.save()
                print("💾 事件保存成功")

                // 数据库保存成功后，更新通知
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    if isNotificationEnabled {
                        print("🔄 重新调度通知...")
                        self.notificationManager.scheduleNotification(for: existingEvent)
                    } else {
                        print("❌ 取消通知...")
                        self.notificationManager.cancelNotification(for: existingEvent)
                    }
                }

                dismiss()
            } catch {
                print("❌ 保存事件失败: \(error)")
            }
        } else {
            // 创建新事件
            let newEvent = EventSwiftData()
            newEvent.title = cleanedTitle
            newEvent.date = dateFormatter.string(from: selectedDate)
            newEvent.cycleType = selectedCycleType.rawValue
            newEvent.notificationTime = timeFormatter.string(from: notificationTime)
            newEvent.isLunar = isLunar
            newEvent.note = note.trimmingCharacters(in: .whitespacesAndNewlines)
            newEvent.color = selectedColor
            newEvent.iconName = selectedIcon
            newEvent.isNotificationEnabled = isNotificationEnabled
            newEvent.advanceNotificationType = advanceNotificationType.rawValue

            context.insert(newEvent)

            print("✅ 创建新事件: \(newEvent.title)")

            // 保存到数据库
            do {
                try context.save()
                print("💾 事件保存成功")

                // 数据库保存成功后，安排通知
                if isNotificationEnabled {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                        print("🔄 调度新事件通知...")
                        self.notificationManager.scheduleNotification(for: newEvent)
                    }
                }

                dismiss()
            } catch {
                print("❌ 保存事件失败: \(error)")
            }
        }
    }

    // MARK: - Helper Properties and Methods

    /// 检查是否应该显示农历选项（基于系统语言）
    private var shouldShowLunarOption: Bool {
        let systemLanguageCode = Locale.preferredLanguages.first ?? "en"
        return systemLanguageCode.hasPrefix("zh")
    }

    /// 创建ISO日期格式器
    private func createISODateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone.current
        return formatter
    }

    /// 创建时间格式器
    private func createHourMinuteFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.locale = Locale.current
        formatter.dateFormat = "HH:mm"
        return formatter
    }


}

#Preview {
    EventEditSwiftDataView(event: nil)
}
