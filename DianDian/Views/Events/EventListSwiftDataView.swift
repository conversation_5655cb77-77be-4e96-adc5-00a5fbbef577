//
//  EventListSwiftDataView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/29.
//

import SwiftUI
import SwiftData

struct EventListSwiftDataView: View {
    @Environment(\.modelContext) private var context
    @EnvironmentObject var appState: AppStateManager
    @StateObject private var notificationManager = NotificationManager()
    
    // SwiftData 查询 - 活跃事件
    @Query(filter: #Predicate<EventSwiftData> { event in
        !event.isArchived
    }, sort: \EventSwiftData.date) 
    var activeEvents: [EventSwiftData]
    
    // SwiftData 查询 - 归档事件（用于计算总数）
    @Query(filter: #Predicate<EventSwiftData> { event in
        event.isArchived
    }) 
    var archivedEvents: [EventSwiftData]
    
    @State private var searchText = ""
    @State private var showingAddEvent = false
    @State private var showingArchivedEvents = false
    @State private var selectedEvent: EventSwiftData?
    @State private var showingEventDetail = false
    @State private var showingPremiumAlert = false
    @State private var showingPremiumView = false
    @State private var editingEvent: EventSwiftData?
    
    private var filteredEvents: [EventSwiftData] {
        if searchText.isEmpty {
            return activeEvents
        } else {
            return activeEvents.filter { event in
                event.title.localizedCaseInsensitiveContains(searchText) ||
                event.note.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    private var groupedEvents: [String: [EventSwiftData]] {
        Dictionary(grouping: filteredEvents) { event in
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyyMM" // 固定格式，如 "202507"

            // 对于永不重复且已过期的事件，使用原始日期分组，但添加特殊前缀确保排序在最后
            if event.cycleTypeEnum == .never && event.daysUntilNext < 0 {
                if let eventDate = event.eventDate {
                    return "zzz_" + formatter.string(from: eventDate) // 添加前缀确保排序在最后
                } else {
                    return "zzz_unknown"
                }
            }

            // 优先使用下次发生日期，如果没有则使用原始日期
            if let nextDate = event.nextOccurrenceDate {
                return formatter.string(from: nextDate)
            } else if let eventDate = event.eventDate {
                return formatter.string(from: eventDate)
            } else {
                return NSLocalizedString("未知", comment: "Unknown")
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                EnhancedSearchBar(
                    text: $searchText,
                    placeholder: "search.placeholder".localized
                )

                // 主内容
                if filteredEvents.isEmpty {
                    EmptyStateView(
                        isSearching: !searchText.isEmpty,
                        onAddEvent: {
                            if canAddMoreEvents() {
                                showingAddEvent = true
                            } else {
                                showingPremiumAlert = true
                            }
                        }
                    )
                } else {
                    EventListSwiftData(
                        events: filteredEvents,
                        groupedEvents: groupedEvents,
                        searchText: searchText,
                        onEventTap: { event in
                            selectedEvent = event
                            showingEventDetail = true
                        },
                        onEventDelete: { event in
                            deleteEvent(event)
                        },
                        onEventArchive: { event in
                            toggleArchiveStatus(for: event)
                        },
                        onEventEdit: { event in
                            editingEvent = event
                        }
                    )
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("tab.events".localized)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        showingArchivedEvents = true
                    }) {
                        Image(systemName: "archivebox")
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        if canAddMoreEvents() {
                            showingAddEvent = true
                        } else {
                            showingPremiumAlert = true
                        }
                    }) {
                        Image(systemName: "plus.circle")
                    }
                }
            }
            .sheet(isPresented: $showingAddEvent) {
                EventEditSwiftDataView(event: nil)
            }
            .sheet(item: $editingEvent) { event in
                EventEditSwiftDataView(event: event)
            }
            .sheet(isPresented: $showingArchivedEvents) {
                ArchivedEventsSwiftDataView()
            }
            .sheet(item: $selectedEvent) { event in
                EventDetailSwiftDataView(event: event)
            }
            .alert("升级到高级版", isPresented: $showingPremiumAlert) {
                Button("升级") {
                    showingPremiumView = true
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("免费版最多只能添加 \(appState.freeEventLimit) 个纪念日（包含已归档）。升级到高级版可以添加无限个纪念日。")
            }
            .sheet(isPresented: $showingPremiumView) {
                PremiumView()
            }
            .refreshable {
                // SwiftData 自动刷新，这里可以添加其他刷新逻辑
            }
        }
    }
    
    // MARK: - 私有方法
    
    private func canAddMoreEvents() -> Bool {
        let totalCount = activeEvents.count + archivedEvents.count
        return appState.isPremiumUser || totalCount < appState.freeEventLimit
    }
    
    private func deleteEvent(_ event: EventSwiftData) {
        withAnimation {
            // 取消通知
            notificationManager.cancelNotification(for: event)
            context.delete(event)
            try? context.save()
        }
    }
    
    private func toggleArchiveStatus(for event: EventSwiftData) {
        withAnimation {
            let _ = event.isArchived
            event.isArchived.toggle()
            event.updatedAt = Date()

            do {
                try context.save()
                print("✅ 事件归档状态更新成功: \(event.isArchived ? "已归档" : "已取消归档")")

                // 处理通知
                if event.isArchived {
                    // 归档时取消通知
                    notificationManager.cancelNotification(for: event)
                    print("🗑️ 已取消归档事件的通知")
                } else {
                    // 取消归档时重新添加通知（如果启用了通知）
                    if event.isNotificationEnabled {
                        notificationManager.scheduleNotification(for: event)
                        print("🔔 已重新调度取消归档事件的通知")
                    }
                }
            } catch {
                print("❌ 更新事件归档状态失败: \(error)")
            }
        }
    }
}

// MARK: - 事件列表组件
struct EventListSwiftData: View {
    let events: [EventSwiftData]
    let groupedEvents: [String: [EventSwiftData]]
    let searchText: String
    let onEventTap: (EventSwiftData) -> Void
    let onEventDelete: (EventSwiftData) -> Void
    let onEventArchive: (EventSwiftData) -> Void
    let onEventEdit: (EventSwiftData) -> Void
    
    // 获取今日纪念日
    private var todayEvents: [EventSwiftData] {
        events.filter { $0.isToday() }
    }
    
    // 获取即将到来的纪念日（未来7天）
    private var upcomingEvents: [EventSwiftData] {
        events.filter { event in
            let days = event.daysUntilNext
            // 对于永不重复的事件，如果已过期则不显示在即将到来中
            if event.cycleTypeEnum == .never && days < 0 {
                return false
            }
            return days > 0 && days <= 7
        }.sorted { $0.daysUntilNext < $1.daysUntilNext }
    }
    
    var body: some View {
        List {
            todayEventsSection
            upcomingEventsSection
            monthlyEventsSection
        }
        .listStyle(PlainListStyle())
    }

    @ViewBuilder
    private var todayEventsSection: some View {
        if !todayEvents.isEmpty && searchText.isEmpty {
            Section("section.today.events".localized) {
                ForEach(todayEvents, id: \.id) { event in
                    eventRowView(event)
                }
            }
        }
    }

    @ViewBuilder
    private var upcomingEventsSection: some View {
        if !upcomingEvents.isEmpty && searchText.isEmpty {
            Section("section.upcoming.events".localized) {
                ForEach(upcomingEvents, id: \.id) { event in
                    eventRowView(event)
                }
            }
        }
    }

    @ViewBuilder
    private var monthlyEventsSection: some View {
        if searchText.isEmpty {
            ForEach(groupedEvents.keys.sorted(), id: \.self) { monthKey in
                Section(header: MonthHeaderView(monthKey: monthKey)) {
                    ForEach(sortedEventsForGroup(monthKey), id: \.id) { event in
                        eventRowView(event)
                    }
                }
            }
        } else {
            Section("section.search.results".localized) {
                ForEach(groupedEvents.keys.sorted(), id: \.self) { monthKey in
                    ForEach(sortedEventsForGroup(monthKey), id: \.id) { event in
                        eventRowView(event)
                    }
                }
            }
        }
    }

    /// 获取指定分组的排序后事件列表
    private func sortedEventsForGroup(_ monthKey: String) -> [EventSwiftData] {
        let events = groupedEvents[monthKey] ?? []

        if monthKey.hasPrefix("zzz_") {
            // 已过期事件分组：按过期时间排序，最近过期的在前面（daysUntilNext从大到小，即-1, -2, -3...）
            return events.sorted { $0.daysUntilNext > $1.daysUntilNext }
        } else {
            // 其他分组：按下次发生时间排序
            return events.sorted { event1, event2 in
                let days1 = event1.daysUntilNext
                let days2 = event2.daysUntilNext
                return days1 < days2
            }
        }
    }

    private func eventRowView(_ event: EventSwiftData) -> some View {
        EventRowSwiftDataView(event: event)
            .listRowInsets(LayoutConstants.standardListRowInsets)
            .listRowSeparator(.hidden)
            .listRowBackground(Color.clear)
            .onTapGesture {
                onEventTap(event)
            }
    }

}

// MARK: - 辅助组件

struct EmptyStateView: View {
    let isSearching: Bool
    let onAddEvent: () -> Void

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: isSearching ? "magnifyingglass" : "gift.fill")
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text(isSearching ? "未找到相关纪念日" : "还没有纪念日")
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(isSearching ? "尝试调整搜索条件" : "点击下方按钮添加第一个纪念日")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            if !isSearching {
                Button(action: onAddEvent) {
                    HStack {
                        Image(systemName: "plus")
                        Text("添加")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.pink)
                    .clipShape(Capsule())
                }
            }
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct MonthHeaderView: View {
    let monthKey: String

    private var formattedMonthYear: String {
        // 处理带有"zzz_"前缀的已过期事件分组
        let actualMonthKey = monthKey.hasPrefix("zzz_") ? String(monthKey.dropFirst(4)) : monthKey

        // 将 monthKey (yyyyMM 格式) 转换回日期
        let inputFormatter = DateFormatter()
        inputFormatter.dateFormat = "yyyyMM" // 固定格式，如 "202507"

        guard let date = inputFormatter.date(from: actualMonthKey) else {
            return monthKey // 如果转换失败，返回原始字符串
        }

        // 使用数字格式的年月显示
        let outputFormatter = DateFormatter()
        outputFormatter.locale = Locale.current

        // 根据语言设置不同的格式
        let systemLanguageCode = Locale.preferredLanguages.first ?? "en"
        if systemLanguageCode.hasPrefix("zh") {
            // 中文：显示 "2025年7月" (使用数字月份)
            outputFormatter.dateFormat = "yyyy年M月"
        } else {
            // 英文及其他语言：显示 "July 2024"
            outputFormatter.dateFormat = "MMMM yyyy"
        }

        return outputFormatter.string(from: date)
    }

    var body: some View {
        HStack {
            Text(formattedMonthYear)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.primary)

            Spacer()
        }
    }
}

#Preview {
    EventListSwiftDataView()
        .environmentObject(AppStateManager())
}
