//
//  ArchivedEventsSwiftDataView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/29.
//

import SwiftUI
import SwiftData

struct ArchivedEventsSwiftDataView: View {
    @Environment(\.modelContext) private var context
    @Environment(\.dismiss) private var dismiss
    @StateObject private var notificationManager = NotificationManager()

    // SwiftData 查询 - 归档事件
    @Query(filter: #Predicate<EventSwiftData> { event in
        event.isArchived
    }, sort: \EventSwiftData.date)
    var archivedEvents: [EventSwiftData]

    @State private var selectedEvent: EventSwiftData?
    @State private var searchText = ""

    private var filteredEvents: [EventSwiftData] {
        if searchText.isEmpty {
            return archivedEvents
        } else {
            return archivedEvents.filter { event in
                event.title.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                if !archivedEvents.isEmpty {
                    EnhancedSearchBar(
                        text: $searchText,
                        placeholder: "搜索..."
                    )
                }

                // 主要内容
                Group {
                    if archivedEvents.isEmpty {
                        VStack(spacing: 20) {
                            Image(systemName: "archivebox")
                                .font(.system(size: 60))
                                .foregroundColor(.secondary)

                            Text("没有归档的纪念日")
                                .font(.title2)
                                .fontWeight(.medium)

                            Text("归档的纪念日会显示在这里")
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else {
                        List {
                            ForEach(filteredEvents, id: \.id) { event in
                                EventRowSwiftDataView(event: event)
                                    .listRowInsets(LayoutConstants.standardListRowInsets)
                                    .listRowSeparator(.hidden)
                                    .listRowBackground(Color.clear)
                                    .onTapGesture {
                                        selectedEvent = event
                                    }
                            }
                        }
                        .listStyle(PlainListStyle())
                        .background(Color(.systemGroupedBackground))
                    }
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("归档")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
            .sheet(item: $selectedEvent) { event in
                EventDetailSwiftDataView(event: event)
            }
        }
    }
    

}

#Preview {
    ArchivedEventsSwiftDataView()
}
