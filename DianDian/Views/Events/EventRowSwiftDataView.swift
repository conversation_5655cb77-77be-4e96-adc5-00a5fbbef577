//
//  EventRowSwiftDataView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/29.
//

import SwiftUI

struct EventRowSwiftDataView: View {
    let event: EventSwiftData

    private var eventColor: Color {
        Color(hex: event.color) ?? .pink
    }

    private var daysText: String {
        let days = event.daysUntilNext
        if days == 0 {
            return "今天"
        } else if days > 0 {
            return "\(days)天"
        } else {
            return "\(abs(days))天"
        }
    }

    private var daysStatus: String {
        let days = event.daysUntilNext
        if days == 0 {
            return "今天"
        } else if days > 0 {
            return "还有"
        } else {
            return "已过"
        }
    }



    private var iconName: String {
        return event.iconName.isEmpty ? "heart.fill" : event.iconName
    }

    // MARK: - 已过期事件样式

    /// 是否为已过期的永不重复事件
    private var isExpiredNeverEvent: Bool {
        return event.cycleTypeEnum == .never && event.daysUntilNext < 0
    }

    /// 文字透明度（已过期事件变暗）
    private var textOpacity: Double {
        return isExpiredNeverEvent ? 0.6 : 1.0
    }

    /// 图标颜色（已过期事件变暗）
    private var adjustedIconColor: Color {
        if isExpiredNeverEvent {
            return eventColor.opacity(0.5)
        } else {
            return eventColor
        }
    }

    /// 右侧文字颜色（已过期事件使用次要颜色）
    private var adjustedRightTextColor: Color {
        if isExpiredNeverEvent {
            return .secondary
        } else {
            let days = event.daysUntilNext
            if days == 0 {
                return .red
            } else if days <= 0 {
                return .red
            } else {
                return .green
            }
        }
    }

    var body: some View {
        HStack(spacing: 16) {
            // 事件图标
            ZStack {
                Circle()
                    .fill(adjustedIconColor)
                    .frame(width: 44, height: 44)

                Image(systemName: iconName)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.white)
                    .opacity(textOpacity)
            }

            // 中间内容
            VStack(alignment: .leading, spacing: 4) {
                // 事件标题和标识
                HStack(spacing: 6) {
                    Text(event.title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                        .opacity(textOpacity)
                        .lineLimit(1)

                    // 已过期标识（如果是已过期的永不重复事件）
                    if isExpiredNeverEvent {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }

                    // 通知标识（如果开启了通知）
                    if event.isNotificationEnabled {
                        HStack(spacing: 2) {
                            Image(systemName: "bell.badge.circle.fill")
                                .font(.system(size: 14))
                                .foregroundColor(.blue)
                                .opacity(textOpacity)
                        }
                    }

                    // 农历标识（如果是农历事件）
                    if event.isLunar {
                        HStack(spacing: 2) {
                            Image(systemName: "moon.circle.fill")
                                .font(.system(size: 14))
                                .foregroundColor(.orange)
                                .opacity(textOpacity)
                        }
                    }

                    Spacer()
                }

                Text(event.formattedNextDateShort)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .opacity(textOpacity)
            }

            Spacer()

            // 右侧内容
            HStack(spacing: 8) {
                // 归档图标（如果已归档）
                if event.isArchived {
                    Image(systemName: "archivebox.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.orange)
                        .opacity(textOpacity)
                }

                VStack(alignment: .trailing, spacing: 4) {
                    Text(daysText)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(adjustedRightTextColor)
                        .opacity(textOpacity)

                    Text(daysStatus)
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                        .opacity(textOpacity)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color("MainBgColor"))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray5), lineWidth: 0.5)
                )
        )
        .contentShape(Rectangle())
    }


}

#Preview {
    let sampleEvent = EventSwiftData()
    sampleEvent.title = "生日"
    sampleEvent.date = "2025-08-15"
    sampleEvent.note = "记得准备礼物"
    sampleEvent.color = "blue"
    sampleEvent.iconName = "heart.fill"
    sampleEvent.isNotificationEnabled = true
    
    return EventRowSwiftDataView(event: sampleEvent)
        .padding()
}
