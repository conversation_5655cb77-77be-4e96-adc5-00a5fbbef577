//
//  MainTabView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import SwiftUI
import SwiftData

// MARK: - 主标签页视图
struct MainTabView: View {
    @Environment(\.modelContext) private var context
    @EnvironmentObject var appState: AppStateManager
    @EnvironmentObject var notificationManager: NotificationManager



    @State private var selectedTab: TabItem = .events
    @State private var refreshID = UUID()
    @State private var hasScheduledNotifications = false

    // SwiftData 查询 - 所有事件
    @Query var allEvents: [EventSwiftData]
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 纪念日页面
            EventListSwiftDataView()
                .tabItem {
                    Image(systemName: selectedTab == .events ? "gift.fill" : "gift.fill")
                    Text("tab.events".localized)
                }
                .tag(TabItem.events)

            // 节日页面
            HolidayListView()
                .tabItem {
                    Image(systemName: selectedTab == .holidays ? "star.fill" : "star.fill")
                    Text("tab.holidays".localized)
                }
                .tag(TabItem.holidays)

            // 日程页面
            ScheduleListView()
                .tabItem {
                    Image(systemName: selectedTab == .schedule ? "calendar.badge.clock" : "calendar.badge.clock")
                    Text("tab.schedule".localized)
                }
                .tag(TabItem.schedule)

            // 更多页面
            MoreView()
                .tabItem {
                    Image(systemName: selectedTab == .more ? "ellipsis.circle.fill" : "ellipsis.circle")
                    Text("tab.more".localized)
                }
                .tag(TabItem.more)
        }
        .accentColor(.pink)
        .id(refreshID) // 强制刷新视图
        .onAppear {
            setupTabBarAppearance()

            // 确保 NotificationManager 的 ModelContext 已设置（双重保险）
            notificationManager.setModelContext(context)

            scheduleNotificationsIfNeeded()
        }

        .onChange(of: notificationManager.isEnabled) { _, isEnabled in
            // 通知权限状态变化时重新调度通知
            if isEnabled {
                print("🔔 通知权限已授权，重新调度通知...")
                notificationManager.rescheduleAllNotifications(for: allEvents)
            } else {
                print("⚠️ 通知权限被拒绝，取消所有通知")
                notificationManager.cancelAllNotifications()
            }
        }
    }
    
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground
        
        // 设置选中状态的颜色
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor.systemPink
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.systemPink
        ]
        
        // 设置未选中状态的颜色
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.systemGray
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.systemGray
        ]
        
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }

    // MARK: - 通知调度
    private func scheduleNotificationsIfNeeded() {
        // 避免重复调度
        guard !hasScheduledNotifications else { return }

        // 检查通知权限
        guard notificationManager.isEnabled else {
            print("⚠️ 通知权限未授权，跳过通知调度")
            return
        }

        // 重新安排所有事件的通知
        print("🔔 开始重新安排所有事件的通知...")
        notificationManager.rescheduleAllNotifications(for: allEvents)
        hasScheduledNotifications = true

        print("✅ 通知调度完成，共处理 \(allEvents.count) 个事件")
    }
}

// MARK: - Tab Item 枚举
enum TabItem: String, CaseIterable {
    case events = "events"
    case holidays = "holidays"
    case schedule = "schedule"
    case more = "more"

    var title: String {
        switch self {
        case .events: return "tab.events".localized
        case .holidays: return "tab.holidays".localized
        case .schedule: return "tab.schedule".localized
        case .more: return "tab.more".localized
        }
    }

    var iconName: String {
        switch self {
        case .events: return "gift.fill"
        case .holidays: return "star.fill"
        case .schedule: return "calendar.badge.clock"
        case .more: return "ellipsis.circle"
        }
    }

    var selectedIconName: String {
        switch self {
        case .events: return "gift.fill"
        case .holidays: return "star.fill"
        case .schedule: return "calendar.badge.clock"
        case .more: return "ellipsis.circle.fill"
        }
    }
}

// MARK: - 预览
#Preview {
    MainTabView()
        .environmentObject(AppStateManager())
}
