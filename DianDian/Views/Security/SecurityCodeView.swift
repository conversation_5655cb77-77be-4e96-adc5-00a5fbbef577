//
//  SecurityCodeView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/28.
//

import SwiftUI

// MARK: - 安全码输入视图
struct SecurityCodeView: View {
    @EnvironmentObject var securityManager: SecurityManager
    @State private var securityCode: String = ""
    @State private var showError: Bool = false
    @State private var errorMessage: String = ""
    @FocusState private var isTextFieldFocused: Bool

    let mode: SecurityCodeMode
    let onComplete: ((String) -> Void)?

    enum SecurityCodeMode {
        case setup
        case authenticate
        case change
    }

    init(mode: SecurityCodeMode, onComplete: ((String) -> Void)? = nil) {
        self.mode = mode
        self.onComplete = onComplete
    }
    
    var body: some View {
        VStack(spacing: 0) {
            Spacer()

            // 安全锁图标
            Image(systemName: "lock.shield")
                .font(.system(size: 60))
                .foregroundColor(.pink)
                .padding(.bottom, 30)

            // 标题
            VStack(spacing: 16) {
                Text(titleText)
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                if !errorMessage.isEmpty {
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .opacity(showError ? 1 : 0)
                        .animation(.easeInOut(duration: 0.3), value: showError)
                }
            }
            .padding(.bottom, 40)
            
            // 密码点显示
            HStack(spacing: 20) {
                ForEach(0..<6, id: \.self) { index in
                    Circle()
                        .fill(index < securityCode.count ? Color.pink : Color.gray.opacity(0.3))
                        .frame(width: 16, height: 16)
                        .scaleEffect(index < securityCode.count ? 1.2 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: securityCode.count)
                }
            }
            .padding(.bottom, 40)
            
            // Continue 按钮
            Button(action: handleContinue) {
                Text("继续")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(securityCode.count == 6 ? Color.pink : Color.gray.opacity(0.5))
                    )
            }
            .disabled(securityCode.count != 6)
            .padding(.horizontal, 40)
            .padding(.bottom, 60)
            
            Spacer()

            // 隐藏的文本框用于触发系统数字键盘
            TextField("", text: $securityCode)
                .keyboardType(.numberPad)
                .focused($isTextFieldFocused)
                .opacity(0)
                .frame(height: 0)
                .onChange(of: securityCode) { _, newValue in
                    // 限制输入长度为6位
                    if newValue.count > 6 {
                        securityCode = String(newValue.prefix(6))
                    }
                    // 只允许数字
                    securityCode = securityCode.filter { $0.isNumber }

                    // 用户开始输入时隐藏错误信息
                    if !newValue.isEmpty && showError {
                        showError = false
                        errorMessage = ""
                    }
                }
                .onAppear {
                    // 页面出现时自动弹出键盘
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        isTextFieldFocused = true
                    }
                }
        }
        .background(Color(.systemBackground))
    }
    
    private var titleText: String {
        switch mode {
        case .setup:
            return "设置安全码"
        case .authenticate:
            return "输入安全码"
        case .change:
            return "输入新的安全码"
        }
    }
    
    private func handleContinue() {
        guard securityCode.count == 6 else { return }
        
        switch mode {
        case .setup:
            securityManager.enableSecurity(with: securityCode)
            onComplete?(securityCode)
            
        case .authenticate:
            if securityManager.authenticate(with: securityCode) {
                onComplete?(securityCode)
            } else {
                showErrorMessage()
            }
            
        case .change:
            onComplete?(securityCode)
        }
    }
    
    private func showErrorMessage() {
        errorMessage = "安全码错误"
        showError = true
        securityCode = ""

        // 震动反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // 重新获取焦点以便用户继续输入
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            isTextFieldFocused = true
        }
    }
}
