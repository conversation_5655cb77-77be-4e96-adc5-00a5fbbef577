//
//  SecurityLockView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/28.
//

import SwiftUI

// MARK: - 安全锁定视图
struct SecurityLockView: View {
    @EnvironmentObject var securityManager: SecurityManager
    
    var body: some View {
        ZStack {
            // 背景
            Color(.systemBackground)
                .ignoresSafeArea()
            
            // 安全码输入界面
            SecurityCodeView(mode: .authenticate) { _ in
                // 认证成功后，SecurityManager 会自动更新 isAuthenticated 状态
            }
        }
    }
}

#Preview {
    SecurityLockView()
        .environmentObject(SecurityManager())
}
