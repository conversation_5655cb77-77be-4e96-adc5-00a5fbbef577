//
//  SecuritySettingsView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/28.
//

import SwiftUI

// MARK: - 安全设置视图
struct SecuritySettingsView: View {
    @EnvironmentObject var securityManager: SecurityManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var showingSetupCode = false
    @State private var showingChangeCode = false
    @State private var showingConfirmDisable = false
    @State private var showingAuthenticateForChange = false
    @State private var showingAuthenticateForDisable = false
    
    var body: some View {
        NavigationView {
            List {
                Section {
                    HStack {
                        Image(systemName: "lock.shield")
                            .foregroundColor(.pink)
                            .frame(width: 24)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("security.password.protection".localized)
                                .font(.body)
                            Text(securityManager.isSecurityEnabled ? "security.enabled".localized : "security.disabled".localized)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Toggle("", isOn: .constant(securityManager.isSecurityEnabled))
                            .disabled(true)
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        if securityManager.isSecurityEnabled {
                            showingAuthenticateForDisable = true
                        } else {
                            showingSetupCode = true
                        }
                    }
                    
                    if securityManager.isSecurityEnabled {
                        Button(action: {
                            showingAuthenticateForChange = true
                        }) {
                            HStack {
                                Image(systemName: "key")
                                    .foregroundColor(.pink)
                                    .frame(width: 24)
                                Text("security.change.code".localized)
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .foregroundColor(.primary)
                    }
                } header: {
                    Text("security.settings".localized)
                } footer: {
                    Text("security.description".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("security.password.protection".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingSetupCode) {
            SecurityCodeView(mode: .setup) { _ in
                showingSetupCode = false
            }
        }
        .sheet(isPresented: $showingChangeCode) {
            SecurityCodeView(mode: .change) { newCode in
                // 这里应该先验证旧密码，但为了简化，直接设置新密码
                securityManager.enableSecurity(with: newCode)
                showingChangeCode = false
            }
        }
        .sheet(isPresented: $showingAuthenticateForChange) {
            SecurityCodeView(mode: .authenticate) { _ in
                showingAuthenticateForChange = false
                showingChangeCode = true
            }
        }
        .sheet(isPresented: $showingAuthenticateForDisable) {
            SecurityCodeView(mode: .authenticate) { _ in
                showingAuthenticateForDisable = false
                showingConfirmDisable = true
            }
        }
        .alert("确认关闭密码保护", isPresented: $showingConfirmDisable) {
            Button("取消", role: .cancel) { }
            Button("关闭", role: .destructive) {
                securityManager.disableSecurity()
            }
        } message: {
            Text("关闭密码保护后，应用将不再需要安全码验证")
        }
    }
}

#Preview {
    SecuritySettingsView()
        .environmentObject(SecurityManager())
}
