//
//  ColorPickerView.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/27.
//

import SwiftUI

// MARK: - 颜色选择器组件
struct ColorPickerView: View {
    @Binding var selectedColor: String
    
    private let colors = [
        // 第一行 - 红色系（鲜亮）
        "FF3B30", // 鲜红色
        "FF6B6B", // 珊瑚红
        "FF2D92", // 玫瑰红
        "E74C3C", // 深红色
        "FF5722", // 橙红色
        "DC143C", // 深红色

        // 第二行 - 橙色系（鲜亮）
        "FF9500", // 鲜橙色
        "FF8C00", // 深橙色
        "FF6347", // 番茄红
        "FF4500", // 橙红色
        "FFA500", // 橙色
        "FF7F50", // 珊瑚色

        // 第三行 - 黄色系（鲜亮）
        "FFCC02", // 鲜黄色
        "FFD700", // 金色
        "FFF200", // 柠檬黄
        "FFEB3B", // 亮黄色
        "FFC107", // 琥珀色
        "FF9800", // 橙黄色

        // 第四行 - 绿色系（鲜亮）
        "34C759", // 鲜绿色
        "00FF7F", // 春绿色
        "32CD32", // 酸橙绿
        "00FA9A", // 中春绿
        "00FF00", // 纯绿色
        "7CFC00", // 草坪绿

        // 第五行 - 青色系（鲜亮）
        "00FFFF", // 青色
        "40E0D0", // 绿松石
        "48CAE4", // 天蓝色
        "00CED1", // 深绿松石
        "20B2AA", // 浅海绿
        "5DADE2", // 蓝色

        // 第六行 - 蓝色系（鲜亮）
        "007AFF", // 鲜蓝色
        "0080FF", // 天蓝色
        "1E90FF", // 道奇蓝
        "4169E1", // 皇家蓝
        "6495ED", // 矢车菊蓝
        "87CEEB", // 天空蓝

        // 第七行 - 紫色系（鲜亮）
        "AF52DE", // 鲜紫色
        "8A2BE2", // 蓝紫色
        "9932CC", // 深兰花紫
        "BA55D3", // 中兰花紫
        "DA70D6", // 兰花紫
        "EE82EE", // 紫罗兰

        // 第八行 - 粉色系（鲜亮）
        "FF69B4", // 热粉色
        "FF1493", // 深粉色
        "FF20B2", // 鲜粉色
        "C71585", // 中紫红色
        "DB7093", // 古紫红色
        "FFB6C1"  // 浅粉色
    ]
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 16) {
                ForEach(colors, id: \.self) { colorHex in
                    ColorCircle(
                        colorHex: colorHex,
                        isSelected: selectedColor == colorHex
                    ) {
                        selectedColor = colorHex
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .padding(.bottom, 40)
        }
        .background(Color(.systemGroupedBackground))
    }
}

// MARK: - 颜色圆圈组件
struct ColorCircle: View {
    let colorHex: String
    let isSelected: Bool
    let action: () -> Void
    
    private var color: Color {
        Color(hex: colorHex) ?? .gray
    }
    
    var body: some View {
        Button(action: action) {
            ZStack {
                Circle()
                    .fill(color)
                    .frame(width: 56, height: 56)
                
                if isSelected {
                    Circle()
                        .stroke(Color.primary, lineWidth: 4)
                        .frame(width: 56, height: 56)
                    
                    Image(systemName: "checkmark")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 图标选择器组件
struct IconPickerView: View {
    @Binding var selectedIcon: String

    private let icons = [
        // 庆祝类
        "gift.fill", "birthday.cake.fill", "heart.fill", "heart.circle.fill", "party.popper.fill",
        "balloon.fill", "sparkles", "app.gift.fill",

        // 节日纪念
        "calendar", "calendar.circle.fill", "calendar.badge.plus", "calendar.badge.clock",
        "bell.fill", "bell.badge.fill", "alarm.fill", "timer",
        "star.fill", "star.circle.fill", "crown.fill", "trophy.fill",
        "medal.fill", "rosette", "flag.fill", "flag.circle.fill", "wand.and.stars",

        // 特殊节日
        "tree.fill", "snowflake", "suit.club.fill", "suit.heart.fill",
        "moon.stars.fill", "sun.and.horizon.fill", "rainbow", "fireworks",
        "tag.fill", "hand.raised.fill", "gift.circle.fill", "balloon.2.fill",

        // 人物类
        "person.fill", "person.wave.2.fill", "person.2.fill", "person.line.dotted.person.fill", "person.2.wave.2.fill",
        "person.3.fill", "figure.stand", "figure.stand.line.dotted.figure.stand", "figure.stand.dress", "figure.arms.open",
        "figure.2.arms.open", "figure.2.and.child.holdinghands", "figure.and.child.holdinghands", "figure.walk", "figure.wave",
        "figure.run", "figure.walk.treadmill",

        // 家庭生活
        "house.fill", "house.and.flag.fill", "building.2.fill", "bed.double.fill",
        "sofa.fill", "chair.fill", "lamp.table.fill", "refrigerator.fill", "video.fill",
        "wifi", "suit.spade.fill", "scissors",

        // 交通出行
        "car.fill", "airplane", "train.side.front.car", "bicycle",
        "bus.fill", "ferry.fill", "sailboat.fill", "scooter",

        // 工作学习
        "briefcase.fill", "graduationcap.fill", "book.fill", "pencil",
        "laptopcomputer", "desktopcomputer", "iphone", "apple.logo", "envelope.fill",
        "printer.fill",

        // 兴趣爱好
        "music.note", "headphones", "camera.fill", "photo.fill",
        "paintbrush.fill", "gamecontroller.fill", "sportscourt.fill", "dumbbell.fill",

        // 健康医疗
        "heart.text.square.fill", "cross.fill", "pills.fill", "stethoscope",
        "medical.thermometer.fill", "bandage.fill", "syringe.fill", "lungs.fill",

        // 自然环境
        "leaf.fill", "sun.max.fill", "moon.fill", "cloud.fill",
        "flame.fill", "drop.fill", "wind", "bolt.fill", "moon.zzz.fill", "sun.min.fill", "hare.fill",
        "tortoise.fill",

        // 日程事件
        "clock.fill", "clock.badge.checkmark.fill", "stopwatch.fill", "hourglass",
        "checkmark.circle.fill", "xmark.circle.fill", "exclamationmark.circle.fill", "questionmark.circle.fill",
        "bookmark.fill", "pin.fill", "paperclip", "folder.fill",

        // 情感表达
        "face.smiling.fill", "face.dashed.fill", "heart.slash.fill", "heart.rectangle.fill",
        "hands.clap.fill", "hand.thumbsup.fill", "hand.wave.fill", "eyes.inverse"
    ]
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 16) {
                ForEach(icons, id: \.self) { iconName in
                    IconCircle(
                        iconName: iconName,
                        isSelected: selectedIcon == iconName
                    ) {
                        selectedIcon = iconName
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .padding(.bottom, 40)
        }
        .background(Color(.systemGroupedBackground))
    }
}

// MARK: - 图标圆圈组件
struct IconCircle: View {
    let iconName: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            ZStack {
                Circle()
                    .fill(isSelected ? Color.pink : Color(.systemGray5))
                    .frame(width: 56, height: 56)

                Image(systemName: iconName)
                    .font(.system(size: 24))
                    .foregroundColor(isSelected ? .white : .primary)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 20) {
        ColorPickerView(selectedColor: .constant("FF6B6B"))

        IconPickerView(selectedIcon: .constant("gift.fill"))
    }
    .padding()
}
