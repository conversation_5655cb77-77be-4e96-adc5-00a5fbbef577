//
//  LunarDatePicker.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/29.
//

import SwiftUI

// MARK: - 农历数据结构
struct LunarMonth: Identifiable, Hashable {
    let id = UUID()
    let month: Int
    let displayName: String
    let isLeapMonth: Bool
}

struct LunarDay: Identifiable, Hashable {
    let id = UUID()
    let day: Int
    let displayName: String
}

// MARK: - 农历日期选择器
struct LunarDatePicker: View {
    @Binding var selectedDate: Date
    @Binding var isLunar: Bool
    @State private var showingDatePicker = false
    @State private var tempDate: Date

    let eventColor: Color
    // 暂时禁用农历功能，直到LunarCalendarManager重新实现

    // 公历日期范围限制：1900年到2100年
    private var dateRange: ClosedRange<Date> {
        let calendar = Calendar.current
        let startComponents = DateComponents(year: 1900, month: 1, day: 1)
        let endComponents = DateComponents(year: 2100, month: 12, day: 31)

        let startDate = calendar.date(from: startComponents) ?? Date()
        let endDate = calendar.date(from: endComponents) ?? Date()

        return startDate...endDate
    }

    init(selectedDate: Binding<Date>, isLunar: Binding<Bool>, eventColor: Color = .blue) {
        self._selectedDate = selectedDate
        self._isLunar = isLunar
        self._tempDate = State(initialValue: selectedDate.wrappedValue)
        self.eventColor = eventColor
    }
    
    var body: some View {
        HStack {
            Image(systemName: "calendar")
                .foregroundColor(eventColor)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("日期")
                    .font(.callout)
                    .foregroundColor(.primary)
                
                if isLunar {
                    Text("农历功能暂不可用")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                } else {
                    Text(selectedDate, style: .date)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Button("选择") {
                tempDate = selectedDate
                showingDatePicker = true
            }
            .foregroundColor(.blue)
        }
        .contentShape(Rectangle())
        .onTapGesture {
            tempDate = selectedDate
            showingDatePicker = true
        }
        .sheet(isPresented: $showingDatePicker) {
            NavigationView {
                VStack(spacing: 20) {
                    if isLunar {
                        Text("农历功能暂时不可用")
                            .foregroundColor(.secondary)
                            .padding()
                    } else {
                        DatePicker("日期", selection: $tempDate, in: dateRange, displayedComponents: .date)
                            .datePickerStyle(.wheel)
                            .labelsHidden()
                    }

                    Spacer()
                }
                .padding()
                .navigationTitle(isLunar ? "农历日期" : "日期")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("取消") {
                            showingDatePicker = false
                        }
                    }
                    
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("确定") {
                            selectedDate = tempDate
                            showingDatePicker = false
                        }
                    }
                }
            }
            .presentationDetents([.medium, .large])
        }
    }
}

// MARK: - 农历日历视图 (暂时禁用)
/*
struct LunarCalendarView: View {
    @Binding var selectedDate: Date
    @State private var selectedYear: Int
    @State private var selectedMonth: Int
    @State private var selectedDay: Int
    @State private var isLeapMonth: Bool

    // 动态获取的农历数据
    @State private var availableMonths: [LunarMonth] = []
    @State private var availableDays: [LunarDay] = []

    // private let lunarManager = LunarCalendarManager.shared
    
    init(selectedDate: Binding<Date>) {
        self._selectedDate = selectedDate

        // 根据传入的日期确定初始年份和农历组件
        let targetDate = selectedDate.wrappedValue
        let gregorianYear = Calendar.current.component(.year, from: targetDate)

        // 获取农历组件 - 这里是关键，直接从公历日期转换为农历日期
        let components = LunarCalendarManager.shared.getLunarComponents(from: targetDate)

        // 使用公历年份作为基准年份（因为我们的选择器是基于公历年份的）
        self._selectedYear = State(initialValue: gregorianYear)
        self._selectedMonth = State(initialValue: components?.month ?? 1)
        self._selectedDay = State(initialValue: components?.day ?? 1)
        self._isLeapMonth = State(initialValue: components?.isLeapMonth ?? false)

        print("🌙 农历日期选择器初始化:")
        print("   目标公历日期: \(targetDate)")
        print("   公历年份: \(gregorianYear)")
        print("   转换后的农历: \(components?.month ?? 0)月\(components?.day ?? 0)日 (闰月: \(components?.isLeapMonth ?? false))")

        // 初始化可用的月份和日期数据
        self._availableMonths = State(initialValue: [])
        self._availableDays = State(initialValue: [])
    }
    
    var body: some View {
        VStack(spacing: 30) {
            // 农历日期显示
            VStack(spacing: 10) {
                // Text("农历日期")
                //     .font(.headline)
                //     .foregroundColor(.primary)
                
                Text(getCurrentLunarDateString())
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(10)
            }
            
            // 选择器
            HStack(spacing: 20) {
                // 年份选择
                VStack {
                    Text("年")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Picker("年", selection: $selectedYear) {
                        ForEach(1900...2100, id: \.self) { year in
                            Text(String(year))
                                .tag(year)
                        }
                    }
                    .pickerStyle(.wheel)
                    .frame(width: 80)
                }
                
                // 月份选择
                VStack {
                    Text("月")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Picker("月", selection: Binding(
                        get: { getCurrentSelectedMonth() },
                        set: { newMonth in
                            if let month = newMonth {
                                selectedMonth = month.month
                                isLeapMonth = month.isLeapMonth
                                updateAvailableDays()
                                updateSelectedDate()
                            }
                        }
                    )) {
                        ForEach(availableMonths, id: \.self) { month in
                            Text(month.displayName)
                                .tag(month as LunarMonth?)
                        }
                    }
                    .pickerStyle(.wheel)
                    .frame(width: 100)
                }

                // 日期选择
                VStack {
                    Text("日")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Picker("日", selection: Binding(
                        get: { getCurrentSelectedDay() },
                        set: { newDay in
                            if let day = newDay {
                                selectedDay = day.day
                                updateSelectedDate()
                            }
                        }
                    )) {
                        ForEach(availableDays, id: \.self) { day in
                            Text(day.displayName)
                                .tag(day as LunarDay?)
                        }
                    }
                    .pickerStyle(.wheel)
                    .frame(width: 80)
                }
            }
            .onAppear {
                // 重新同步日期 - 确保使用最新的selectedDate值
                let targetDate = selectedDate
                let gregorianYear = Calendar.current.component(.year, from: targetDate)
                let components = LunarCalendarManager.shared.getLunarComponents(from: targetDate)

                selectedYear = gregorianYear
                selectedMonth = components?.month ?? 1
                selectedDay = components?.day ?? 1
                isLeapMonth = components?.isLeapMonth ?? false

                print("🌙 农历选择器出现，重新同步日期:")
                print("   目标公历日期: \(targetDate)")
                print("   同步后的农历: \(selectedMonth)月\(selectedDay)日 (闰月: \(isLeapMonth))")

                updateAvailableMonths()
                updateAvailableDays()

                // 验证当前选择是否有效，如果无效则重新设置
                validateAndAdjustSelection()
            }
            .onChange(of: selectedYear) {
                updateAvailableMonths()
                updateAvailableDays()
                updateSelectedDate()
            }
            
            // 对应公历日期显示
            if let solarDate = lunarManager.lunarToSolar(lunarYear: selectedYear,
                                                        lunarMonth: selectedMonth,
                                                        lunarDay: selectedDay,
                                                        isLeapMonth: isLeapMonth) {
                VStack(spacing: 5) {
                    Text("对应公历")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(solarDate, style: .date)
                        .font(.callout)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 10)
            }
        }
        .padding()
    }
    
    // MARK: - 辅助方法

    private func updateAvailableMonths() {
        availableMonths = lunarManager.getAvailableLunarMonths(for: selectedYear)

        // 确保当前选择的月份在可用列表中
        if !availableMonths.contains(where: { $0.month == selectedMonth && $0.isLeapMonth == isLeapMonth }) {
            if let firstMonth = availableMonths.first {
                selectedMonth = firstMonth.month
                isLeapMonth = firstMonth.isLeapMonth
            }
        }
    }

    private func updateAvailableDays() {
        availableDays = lunarManager.getAvailableLunarDays(
            for: selectedYear,
            lunarMonth: selectedMonth,
            isLeapMonth: isLeapMonth
        )

        // 确保当前选择的日期在可用列表中
        if !availableDays.contains(where: { $0.day == selectedDay }) {
            if let firstDay = availableDays.first {
                selectedDay = firstDay.day
            }
        }
    }

    private func getCurrentSelectedMonth() -> LunarMonth? {
        return availableMonths.first { $0.month == selectedMonth && $0.isLeapMonth == isLeapMonth }
    }

    private func getCurrentSelectedDay() -> LunarDay? {
        return availableDays.first { $0.day == selectedDay }
    }

    private func getCurrentLunarDateString() -> String {
        let monthName = getCurrentSelectedMonth()?.displayName ?? "\(selectedMonth)月"
        let dayName = getCurrentSelectedDay()?.displayName ?? "\(selectedDay)日"
        return "\(selectedYear)年\(monthName)\(dayName)"
    }

    private func updateSelectedDate() {
        // 使用更准确的查找方法
        if let solarDate = lunarManager.findLunarDate(
            gregorianYear: selectedYear,
            lunarMonth: selectedMonth,
            lunarDay: selectedDay,
            isLeapMonth: isLeapMonth
        ) {
            selectedDate = solarDate
            print("🌙 更新选择日期: \(selectedYear)年\(selectedMonth)月\(selectedDay)日 -> \(solarDate)")
        } else {
            print("❌ 无法找到对应的公历日期: \(selectedYear)年\(selectedMonth)月\(selectedDay)日 (闰月: \(isLeapMonth))")
        }
    }

    /// 验证并调整当前选择，确保选择的农历日期在当前年份中存在
    private func validateAndAdjustSelection() {
        // 检查当前选择的月份是否在可用列表中
        let currentMonthExists = availableMonths.contains { month in
            month.month == selectedMonth && month.isLeapMonth == isLeapMonth
        }

        if !currentMonthExists {
            print("⚠️ 当前选择的月份不存在: \(selectedMonth)月 (闰月: \(isLeapMonth))")
            // 尝试找到最接近的月份
            if let closestMonth = availableMonths.first(where: { $0.month == selectedMonth }) {
                selectedMonth = closestMonth.month
                isLeapMonth = closestMonth.isLeapMonth
                print("✅ 调整为最接近的月份: \(selectedMonth)月 (闰月: \(isLeapMonth))")
            } else if let firstMonth = availableMonths.first {
                selectedMonth = firstMonth.month
                isLeapMonth = firstMonth.isLeapMonth
                print("✅ 调整为第一个可用月份: \(selectedMonth)月 (闰月: \(isLeapMonth))")
            }
            // 月份改变后需要重新更新可用日期
            updateAvailableDays()
        }

        // 检查当前选择的日期是否在可用列表中
        let currentDayExists = availableDays.contains { day in
            day.day == selectedDay
        }

        if !currentDayExists {
            print("⚠️ 当前选择的日期不存在: \(selectedDay)日")
            if let firstDay = availableDays.first {
                selectedDay = firstDay.day
                print("✅ 调整为第一个可用日期: \(selectedDay)日")
            }
        }

        // 最后更新选择的日期
        updateSelectedDate()
    }
}
*/

// MARK: - 预览
#Preview {
    VStack {
        LunarDatePicker(selectedDate: .constant(Date()), isLunar: .constant(false))
        
        Divider()
        
        LunarDatePicker(selectedDate: .constant(Date()), isLunar: .constant(true))
    }
    .padding()
}
