//
//  EnhancedSearchBar.swift
//  DianDian
//
//  Created by LeeHom on 2025/7/28.
//

import SwiftUI

// MARK: - 增强搜索栏
struct EnhancedSearchBar: View {
    @Binding var text: String
    let placeholder: String
    @FocusState private var isSearchFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            Spacer()
                .frame(height: 8)

            HStack(spacing: 0) {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                        .frame(width: 20)

                    TextField(placeholder, text: $text)
                        .textFieldStyle(PlainTextFieldStyle())
                        .submitLabel(.search)
                        .focused($isSearchFocused)

                    if !text.isEmpty {
                        Button(action: {
                            text = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.horizontal, 10)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(Color(.systemGray5))
                )

                // 取消按钮（仅在搜索框获得焦点时显示）
                if isSearchFocused {
                    But<PERSON>("取消") {
                        text = ""
                        isSearchFocused = false
                        hideKeyboard()
                    }
                    .foregroundColor(.blue)
                    .padding(.leading, 8)
                    .transition(.move(edge: .trailing).combined(with: .opacity))
                }
            }
            .standardHorizontalPadding()

            Spacer()
                .frame(height: 8)
        }
    }

    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - 预览
#Preview {
    VStack {
        EnhancedSearchBar(
            text: .constant(""),
            placeholder: "搜索..."
        )
        
        EnhancedSearchBar(
            text: .constant("测试搜索"),
            placeholder: "搜索..."
        )
        
        Spacer()
    }
    .padding()
}
