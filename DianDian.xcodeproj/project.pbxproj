// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		CD074F2A2E3F2BD700299ED4 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CD074F0E2E3F29F000299ED4 /* WidgetKit.framework */; };
		CD074F2B2E3F2BD700299ED4 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CD074F102E3F29F000299ED4 /* SwiftUI.framework */; };
		CD074F3A2E3F2BD800299ED4 /* DianDianWidget.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = CD074F292E3F2BD700299ED4 /* DianDianWidget.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		CD074F4B2E3F2D7C00299ED4 /* SwiftData.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CD074F4A2E3F2D7C00299ED4 /* SwiftData.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		CD074F382E3F2BD800299ED4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = CD81383A2E364E64003349A3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CD074F282E3F2BD700299ED4;
			remoteInfo = DianDianWidgetExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		CD074F242E3F29F000299ED4 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				CD074F3A2E3F2BD800299ED4 /* DianDianWidget.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		CD074F0E2E3F29F000299ED4 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = /System/Library/Frameworks/WidgetKit.framework; sourceTree = "<absolute>"; };
		CD074F102E3F29F000299ED4 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = /System/Library/Frameworks/SwiftUI.framework; sourceTree = "<absolute>"; };
		CD074F292E3F2BD700299ED4 /* DianDianWidget.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = DianDianWidget.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		CD074F4A2E3F2D7C00299ED4 /* SwiftData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftData.framework; path = System/Library/Frameworks/SwiftData.framework; sourceTree = SDKROOT; };
		CD5158562E3B019800F51D8C /* DianDian.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DianDian.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		CD074F3E2E3F2BD800299ED4 /* Exceptions for "DianDianWidget" folder in "DianDianWidget" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = CD074F282E3F2BD700299ED4 /* DianDianWidget */;
		};
		CD074F4D2E3F2DA200299ED4 /* Exceptions for "DianDian" folder in "DianDianWidget" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Models/EventSwiftData.swift,
				Models/WidgetEventData.swift,
				Services/WidgetDataProvider.swift,
				Services/WidgetURLHandler.swift,
				ViewModels/AppStateManager.swift,
			);
			target = CD074F282E3F2BD700299ED4 /* DianDianWidget */;
		};
		CDDBBA312E3E0D9E008F068D /* Exceptions for "DianDian" folder in "DianDian" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = CD8138412E364E64003349A3 /* DianDian */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		CD074F2C2E3F2BD700299ED4 /* DianDianWidget */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				CD074F3E2E3F2BD800299ED4 /* Exceptions for "DianDianWidget" folder in "DianDianWidget" target */,
			);
			path = DianDianWidget;
			sourceTree = "<group>";
		};
		CD8138442E364E64003349A3 /* DianDian */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				CDDBBA312E3E0D9E008F068D /* Exceptions for "DianDian" folder in "DianDian" target */,
				CD074F4D2E3F2DA200299ED4 /* Exceptions for "DianDian" folder in "DianDianWidget" target */,
			);
			path = DianDian;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		CD074F262E3F2BD700299ED4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CD074F4B2E3F2D7C00299ED4 /* SwiftData.framework in Frameworks */,
				CD074F2B2E3F2BD700299ED4 /* SwiftUI.framework in Frameworks */,
				CD074F2A2E3F2BD700299ED4 /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CD81383F2E364E64003349A3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		CD074F0D2E3F29F000299ED4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				CD074F4A2E3F2D7C00299ED4 /* SwiftData.framework */,
				CD074F0E2E3F29F000299ED4 /* WidgetKit.framework */,
				CD074F102E3F29F000299ED4 /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		CD8138392E364E64003349A3 = {
			isa = PBXGroup;
			children = (
				CD8138442E364E64003349A3 /* DianDian */,
				CD074F2C2E3F2BD700299ED4 /* DianDianWidget */,
				CD074F0D2E3F29F000299ED4 /* Frameworks */,
				CD5158562E3B019800F51D8C /* DianDian.app */,
				CD074F292E3F2BD700299ED4 /* DianDianWidget.appex */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		CD074F282E3F2BD700299ED4 /* DianDianWidget */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CD074F3B2E3F2BD800299ED4 /* Build configuration list for PBXNativeTarget "DianDianWidget" */;
			buildPhases = (
				CD074F252E3F2BD700299ED4 /* Sources */,
				CD074F262E3F2BD700299ED4 /* Frameworks */,
				CD074F272E3F2BD700299ED4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				CD074F2C2E3F2BD700299ED4 /* DianDianWidget */,
			);
			name = DianDianWidget;
			packageProductDependencies = (
			);
			productName = DianDianWidgetExtension;
			productReference = CD074F292E3F2BD700299ED4 /* DianDianWidget.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		CD8138412E364E64003349A3 /* DianDian */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CD81384D2E364E65003349A3 /* Build configuration list for PBXNativeTarget "DianDian" */;
			buildPhases = (
				CD81383E2E364E64003349A3 /* Sources */,
				CD81383F2E364E64003349A3 /* Frameworks */,
				CD8138402E364E64003349A3 /* Resources */,
				CD074F242E3F29F000299ED4 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				CD074F392E3F2BD800299ED4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				CD8138442E364E64003349A3 /* DianDian */,
			);
			name = DianDian;
			packageProductDependencies = (
			);
			productName = DianDian;
			productReference = CD5158562E3B019800F51D8C /* DianDian.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		CD81383A2E364E64003349A3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					CD074F282E3F2BD700299ED4 = {
						CreatedOnToolsVersion = 16.4;
						LastSwiftMigration = 1640;
					};
					CD8138412E364E64003349A3 = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = CD81383D2E364E64003349A3 /* Build configuration list for PBXProject "DianDian" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				"zh-Hans",
				"zh-Hant",
			);
			mainGroup = CD8138392E364E64003349A3;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = CD8138392E364E64003349A3;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				CD8138412E364E64003349A3 /* DianDian */,
				CD074F282E3F2BD700299ED4 /* DianDianWidget */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		CD074F272E3F2BD700299ED4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CD8138402E364E64003349A3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		CD074F252E3F2BD700299ED4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CD81383E2E364E64003349A3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		CD074F392E3F2BD800299ED4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = CD074F282E3F2BD700299ED4 /* DianDianWidget */;
			targetProxy = CD074F382E3F2BD800299ED4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		CD074F3C2E3F2BD800299ED4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = DianDianWidget/DianDianWidget.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 311;
				DEVELOPMENT_TEAM = 7BSRF622NC;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = DianDianWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = DianDian;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.1.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.iamlihongking.dotdot.widget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		CD074F3D2E3F2BD800299ED4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = DianDianWidget/DianDianWidget.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 311;
				DEVELOPMENT_TEAM = 7BSRF622NC;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = DianDianWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = DianDian;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.1.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.iamlihongking.dotdot.widget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		CD81384B2E364E65003349A3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 7BSRF622NC;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		CD81384C2E364E65003349A3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 7BSRF622NC;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		CD81384E2E364E65003349A3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = DianDian/DianDian.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 311;
				DEVELOPMENT_TEAM = 7BSRF622NC;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = DianDian/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = DianDian;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSCalendarsUsageDescription = "需要访问日历以显示节日信息";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.1.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.iamlihongking.dotdot;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		CD81384F2E364E65003349A3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = DianDian/DianDian.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 311;
				DEVELOPMENT_TEAM = 7BSRF622NC;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = DianDian/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = DianDian;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSCalendarsUsageDescription = "需要访问日历以显示节日信息";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.1.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.iamlihongking.dotdot;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		CD074F3B2E3F2BD800299ED4 /* Build configuration list for PBXNativeTarget "DianDianWidget" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CD074F3C2E3F2BD800299ED4 /* Debug */,
				CD074F3D2E3F2BD800299ED4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CD81383D2E364E64003349A3 /* Build configuration list for PBXProject "DianDian" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CD81384B2E364E65003349A3 /* Debug */,
				CD81384C2E364E65003349A3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CD81384D2E364E65003349A3 /* Build configuration list for PBXNativeTarget "DianDian" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CD81384E2E364E65003349A3 /* Debug */,
				CD81384F2E364E65003349A3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = CD81383A2E364E64003349A3 /* Project object */;
}
