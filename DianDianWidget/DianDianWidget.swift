//
//  DianDianWidget.swift
//  DianDianWidget
//
//  Created by LeeHom on 2025/8/3.
//

import WidgetKit
import SwiftUI
import SwiftData

// MARK: - Color Extension for Widget
extension Color {
    init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - Timeline Entry
struct SimpleEntry: TimelineEntry {
    let date: Date
    let events: [WidgetEventData]
}

// MARK: - Timeline Provider
struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(date: Date(), events: getWidgetEvents())
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let entry = SimpleEntry(date: Date(), events: getWidgetEvents())
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let events = getWidgetEvents()
        let entry = SimpleEntry(date: Date(), events: events)
        
        // 每15分钟更新一次
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 15, to: Date()) ?? Date()
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        completion(timeline)
    }
    
    private func getWidgetEvents() -> [WidgetEventData] {
        let dataProvider = WidgetDataProvider.shared
        let events = dataProvider.getUpcomingEvents(limit: 10)
        
        if events.isEmpty {
            return dataProvider.getSampleEvents()
        }
        
        return dataProvider.convertToWidgetData(events)
    }
}

// MARK: - Widget Entry View
struct DianDianWidgetEntryView: View {
    var entry: Provider.Entry
    @Environment(\.widgetFamily) var family

    var body: some View {
        switch family {
        case .systemSmall:
            SmallWidgetView(events: entry.events)
        case .systemMedium:
            MediumWidgetView(events: entry.events)
        default:
            SmallWidgetView(events: entry.events)
        }
    }
}

// MARK: - Widget Configuration
struct DianDianWidget: Widget {
    let kind: String = "DianDianWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            DianDianWidgetEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("DianDian")
        .description("即将到来的纪念日")
        .supportedFamilies([.systemSmall, .systemMedium]) // 👈 只支持 small 和 medium
    }
}

// MARK: - Widget Bundle
@main
struct DianDianWidgetBundle: WidgetBundle {
    var body: some Widget {
        DianDianWidget()
    }
}

// MARK: - Preview
#Preview(as: .systemSmall) {
    DianDianWidget()
} timeline: {
    SimpleEntry(date: .now, events: WidgetDataProvider.shared.getSampleEvents())
}
