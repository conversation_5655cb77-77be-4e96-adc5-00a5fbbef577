//
//  MediumWidgetView.swift
//  DianDianWidget
//
//  Created by LeeHom on 2025/8/3.
//

import SwiftUI
import WidgetKit

struct MediumWidgetView: View {
    let events: [WidgetEventData]
    @Environment(\.colorScheme) var colorScheme

    private var upcomingEvents: [WidgetEventData] {
        Array(events.filter { $0.daysUntilNext >= 0 }.prefix(4))
    }

    private var nextEvent: WidgetEventData? {
        upcomingEvents.first
    }

    // 主题色配置
    private var primaryColor: Color {
        if let event = nextEvent, let color = Color(hex: event.color) {
            return color
        }
        return .blue
    }

    var body: some View {
        ZStack {
            // 事件列表
            VStack(alignment: .leading, spacing: 12) {
                // 事件列表
                VStack(spacing: 8) {
                    ForEach(upcomingEvents.prefix(3), id: \.id) { event in
                        HStack(spacing: 10) {
                            // 图标
                            ZStack {
                                Circle()
                                    .fill(primaryColor.opacity(0.25))
                                    .frame(width: 32, height: 32)

                                Image(systemName: event.iconName)
                                    .font(.system(size: 12, weight: .semibold))
                                    .foregroundColor(primaryColor)
                            }

                            // 事件信息
                            VStack(alignment: .leading, spacing: 1) {
                                HStack {
                                    Text(event.title)
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(.primary)
                                        .lineLimit(1)

                                    if event.isLunar {
                                        Image(systemName: "moon.circle.fill")
                                            .font(.system(size: 12))
                                            .foregroundColor(.orange)
                                    }
                                }
                            }

                            Spacer()

                            // 天数
                            Text("\(event.daysUntilNext)")
                                .font(.system(size: 16, weight: .bold, design: .rounded))
                                .foregroundColor(primaryColor)
                        }
                        .padding(.horizontal, 6)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    colorScheme == .dark
                                    ? Color(.systemGray5)
                                    : Color(.systemGray3).opacity(0.65)
                                )
                        )
                    }
                }
            }
        }
        .widgetURL(URL(string: "diandian://events"))
    }
}
