//
//  SmallWidgetView.swift
//  DianDianWidget
//
//  Created by LeeHom on 2025/8/3.
//

import SwiftUI
import WidgetKit

struct SmallWidgetView: View {
    let events: [WidgetEventData]
    @Environment(\.colorScheme) var colorScheme

    private var nextEvent: WidgetEventData? {
        events.first { $0.daysUntilNext >= 0 }
    }

    // 主题色配置
    private var primaryColor: Color {
        if let event = nextEvent, let color = Color(hex: event.color) {
            return color
        }

        return .blue
    }

    var body: some View {
        ZStack {
            VStack(spacing: 10) {
                if let event = nextEvent {
                    VStack(spacing: 8) {
                        // 图标容器
                        ZStack {
                            Circle()
                                .fill(primaryColor.opacity(0.25))
                                .frame(width: 50, height: 50)

                            Image(systemName: event.iconName)
                                .font(.title2)
                                .fontWeight(.semibold)
                                .foregroundColor(primaryColor)
                        }

                        // 标题
                        Text(event.title)
                            .font(.system(size: 14, weight: .semibold, design: .rounded))
                            .foregroundColor(.primary)
                            .lineLimit(2)
                            .multilineTextAlignment(.center)

                        // 天数显示
                        VStack(spacing: 2) {
                            Text("\(event.daysUntilNext)")
                                .font(.system(size: 28, weight: .bold, design: .rounded))
                                .foregroundColor(primaryColor)

//                            Text(event.daysUntilNext == 0 ? "今天" : "天后")
//                                .font(.system(size: 10, weight: .medium, design: .rounded))
//                                .foregroundColor(.secondary)
                        }

                        // 农历标识
                        if event.isLunar {
                            HStack {
                                Image(systemName: "moon.circle.fill")
                                    .font(.system(size: 12))
                            }
                            .foregroundColor(.orange)
                        }
                    }
                } else {
                    VStack(spacing: 12) {
                        Image(systemName: "tray")
                            .font(.system(size: 28))
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(8)
        }
        .widgetURL(URL(string: "diandian://events"))
    }

}
