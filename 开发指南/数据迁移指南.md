# 数据迁移指南

## 📊 从旧版本迁移数据到新版本

### 旧版本数据结构分析

基于备份的代码分析，旧版本使用的数据模型：

```swift
// 旧版本的 DianDianEventData
class DianDianEventData: Object {
    @objc dynamic var id: Int = 1
    @objc dynamic var eventTitle: String = "事件标题"
    @objc dynamic var eventDate: String = "2019-07-19"
    @objc dynamic var eventNoticeTime: String = "08:30"
    @objc dynamic var eventType: Int = 1
    @objc dynamic var eventCycleType: Int = 1
    @objc dynamic var eventTagColor: String = "ffffff"
    @objc dynamic var isLunar: Int = 0
    @objc dynamic var isArchive: Int = 0
    @objc dynamic var advanceDaysType: Int = -1
    @objc dynamic var eventNoticeDate: String = ""
    @objc dynamic var eventIcon: String = "icon-gift-send"
    @objc dynamic var eventBgImgName: String = "bg0"
    @objc dynamic var isCustomizeBgImg: Int = 0
    @objc dynamic var eventNote: String = ""
    @objc dynamic var eventLeftDays: Int = 0
}
```

### 新版本数据结构

```swift
// 新版本的 EventModel
class EventModel: Object {
    @Persisted var id: String = UUID().uuidString
    @Persisted var title: String = ""
    @Persisted var date: String = ""
    @Persisted var notificationTime: String = "09:00"
    @Persisted var cycleType: Int = 1
    @Persisted var color: String = "FF6B6B"
    @Persisted var iconName: String = "gift"
    @Persisted var backgroundImageName: String = "bg0"
    @Persisted var isLunar: Bool = false
    @Persisted var isArchived: Bool = false
    @Persisted var advanceNotificationType: Int = 0
    @Persisted var note: String = ""
    @Persisted var isNotificationEnabled: Bool = true
    @Persisted var createdAt: Date = Date()
    @Persisted var updatedAt: Date = Date()
}
```

## 🔄 数据迁移策略

### 方案一：Realm 数据库迁移（推荐）

如果新旧版本都使用 Realm，可以通过数据库迁移实现：

```swift
// 在 DianDianApp.swift 中配置迁移
private func configureRealm() {
    let config = Realm.Configuration(
        schemaVersion: 10, // 新版本号
        migrationBlock: { migration, oldSchemaVersion in
            if oldSchemaVersion < 10 {
                // 从旧版本迁移到新版本
                migration.enumerateObjects(ofType: "DianDianEventData") { oldObject, newObject in
                    guard let oldObject = oldObject, let newObject = newObject else { return }
                    
                    // 迁移基础字段
                    newObject["id"] = UUID().uuidString
                    newObject["title"] = oldObject["eventTitle"]
                    newObject["date"] = oldObject["eventDate"]
                    newObject["notificationTime"] = oldObject["eventNoticeTime"]
                    newObject["cycleType"] = oldObject["eventCycleType"]
                    
                    // 迁移颜色（添加#前缀）
                    if let colorString = oldObject["eventTagColor"] as? String {
                        newObject["color"] = colorString.uppercased()
                    }
                    
                    // 迁移图标名称
                    if let iconName = oldObject["eventIcon"] as? String {
                        newObject["iconName"] = mapOldIconToNew(iconName)
                    }
                    
                    // 迁移背景图片
                    newObject["backgroundImageName"] = oldObject["eventBgImgName"]
                    
                    // 迁移布尔值（Int -> Bool）
                    newObject["isLunar"] = (oldObject["isLunar"] as? Int) == 1
                    newObject["isArchived"] = (oldObject["isArchive"] as? Int) == 1
                    
                    // 迁移提前通知类型
                    newObject["advanceNotificationType"] = oldObject["advanceDaysType"]
                    
                    // 迁移备注
                    newObject["note"] = oldObject["eventNote"]
                    
                    // 设置新字段的默认值
                    newObject["isNotificationEnabled"] = true
                    newObject["createdAt"] = Date()
                    newObject["updatedAt"] = Date()
                }
            }
        }
    )
    Realm.Configuration.defaultConfiguration = config
}

// 图标名称映射
private func mapOldIconToNew(_ oldIconName: String) -> String {
    let iconMapping: [String: String] = [
        "icon-gift-send": "gift",
        "icon-birthday": "birthday",
        "icon-heart": "heart",
        "icon-anniversary": "anniversary",
        // 添加更多映射...
    ]
    return iconMapping[oldIconName] ?? "gift"
}
```

### 方案二：数据导出导入

如果直接迁移有问题，可以通过导出导入的方式：

#### 1. 从旧版本导出数据

```swift
// 在旧版本中添加导出功能
func exportDataToJSON() -> String? {
    let realm = try! Realm()
    let events = realm.objects(DianDianEventData.self)
    
    var exportData: [[String: Any]] = []
    
    for event in events {
        let eventDict: [String: Any] = [
            "id": event.id,
            "title": event.eventTitle,
            "date": event.eventDate,
            "notificationTime": event.eventNoticeTime,
            "cycleType": event.eventCycleType,
            "color": event.eventTagColor,
            "iconName": event.eventIcon,
            "backgroundImageName": event.eventBgImgName,
            "isLunar": event.isLunar == 1,
            "isArchived": event.isArchive == 1,
            "advanceNotificationType": event.advanceDaysType,
            "note": event.eventNote
        ]
        exportData.append(eventDict)
    }
    
    do {
        let jsonData = try JSONSerialization.data(withJSONObject: exportData, options: .prettyPrinted)
        return String(data: jsonData, encoding: .utf8)
    } catch {
        print("导出失败: \(error)")
        return nil
    }
}
```

#### 2. 在新版本中导入数据

```swift
// 在新版本中添加导入功能
func importDataFromJSON(_ jsonString: String) {
    guard let jsonData = jsonString.data(using: .utf8),
          let eventArray = try? JSONSerialization.jsonObject(with: jsonData) as? [[String: Any]] else {
        print("JSON 解析失败")
        return
    }
    
    let realm = try! Realm()
    
    try! realm.write {
        for eventDict in eventArray {
            let newEvent = EventModel()
            
            newEvent.id = UUID().uuidString
            newEvent.title = eventDict["title"] as? String ?? ""
            newEvent.date = eventDict["date"] as? String ?? ""
            newEvent.notificationTime = eventDict["notificationTime"] as? String ?? "09:00"
            newEvent.cycleType = eventDict["cycleType"] as? Int ?? 1
            newEvent.color = (eventDict["color"] as? String ?? "FF6B6B").uppercased()
            newEvent.iconName = mapOldIconToNew(eventDict["iconName"] as? String ?? "gift")
            newEvent.backgroundImageName = eventDict["backgroundImageName"] as? String ?? "bg0"
            newEvent.isLunar = eventDict["isLunar"] as? Bool ?? false
            newEvent.isArchived = eventDict["isArchived"] as? Bool ?? false
            newEvent.advanceNotificationType = eventDict["advanceNotificationType"] as? Int ?? 0
            newEvent.note = eventDict["note"] as? String ?? ""
            newEvent.isNotificationEnabled = true
            newEvent.createdAt = Date()
            newEvent.updatedAt = Date()
            
            realm.add(newEvent)
        }
    }
}
```

### 方案三：iCloud 数据迁移

如果旧版本有 iCloud 同步功能，可以通过 CloudKit 迁移：

```swift
// 从 CloudKit 读取旧数据并转换
func migrateFromCloudKit() {
    let privateDb = CKContainer.default().privateCloudDatabase
    let query = CKQuery(recordType: "DianDianEventData", predicate: NSPredicate(value: true))
    
    privateDb.perform(query, inZoneWith: nil) { records, error in
        guard let records = records, error == nil else {
            print("CloudKit 查询失败: \(error?.localizedDescription ?? "")")
            return
        }
        
        let realm = try! Realm()
        
        try! realm.write {
            for record in records {
                let newEvent = EventModel()
                
                newEvent.id = UUID().uuidString
                newEvent.title = record["eventTitle"] as? String ?? ""
                newEvent.date = record["eventDate"] as? String ?? ""
                newEvent.notificationTime = record["eventNoticeTime"] as? String ?? "09:00"
                newEvent.cycleType = record["eventCycleType"] as? Int ?? 1
                newEvent.color = (record["eventTagColor"] as? String ?? "FF6B6B").uppercased()
                newEvent.iconName = mapOldIconToNew(record["eventIcon"] as? String ?? "gift")
                newEvent.backgroundImageName = record["eventBgImgName"] as? String ?? "bg0"
                newEvent.isLunar = (record["isLunar"] as? Int ?? 0) == 1
                newEvent.isArchived = (record["isArchive"] as? Int ?? 0) == 1
                newEvent.advanceNotificationType = record["advanceDaysType"] as? Int ?? 0
                newEvent.note = record["eventNote"] as? String ?? ""
                newEvent.isNotificationEnabled = true
                newEvent.createdAt = Date()
                newEvent.updatedAt = Date()
                
                realm.add(newEvent)
            }
        }
    }
}
```

## 🔧 迁移工具实现

### 创建迁移管理器

```swift
// DataMigrationManager.swift
class DataMigrationManager {
    static let shared = DataMigrationManager()
    
    private init() {}
    
    func checkAndPerformMigration() {
        let userDefaults = UserDefaults.standard
        let migrationKey = "DataMigrationCompleted_v4"
        
        if !userDefaults.bool(forKey: migrationKey) {
            performMigration { success in
                if success {
                    userDefaults.set(true, forKey: migrationKey)
                    print("数据迁移完成")
                } else {
                    print("数据迁移失败")
                }
            }
        }
    }
    
    private func performMigration(completion: @escaping (Bool) -> Void) {
        // 检查是否存在旧版本数据
        if hasOldVersionData() {
            // 执行迁移
            migrateOldData { success in
                completion(success)
            }
        } else {
            completion(true)
        }
    }
    
    private func hasOldVersionData() -> Bool {
        // 检查是否存在旧版本的 Realm 文件或其他数据
        let oldRealmPath = Realm.Configuration.defaultConfiguration.fileURL?.path ?? ""
        return FileManager.default.fileExists(atPath: oldRealmPath)
    }
    
    private func migrateOldData(completion: @escaping (Bool) -> Void) {
        // 实现具体的迁移逻辑
        // 这里可以选择上述任一方案
        completion(true)
    }
}
```

### 在应用启动时调用迁移

```swift
// 在 DianDianApp.swift 的 setupApplication() 中添加
private func setupApplication() {
    // 配置 Realm 数据库
    configureRealm()
    
    // 执行数据迁移
    DataMigrationManager.shared.checkAndPerformMigration()
    
    // 其他初始化代码...
}
```

## ⚠️ 注意事项

### 1. 备份数据
在执行迁移之前，确保用户数据已经备份，以防迁移失败。

### 2. 测试迁移
在不同的数据情况下测试迁移功能：
- 空数据库
- 少量数据
- 大量数据
- 损坏的数据

### 3. 用户体验
- 在迁移过程中显示进度指示器
- 提供迁移失败的恢复选项
- 允许用户手动触发迁移

### 4. 性能考虑
- 大量数据的迁移可能需要时间
- 考虑在后台线程执行迁移
- 分批处理大量数据

---

通过这个迁移指南，你可以确保用户从旧版本升级到新版本时，数据能够完整地保留下来。建议优先使用 Realm 的内置迁移功能，这是最可靠和高效的方式。
