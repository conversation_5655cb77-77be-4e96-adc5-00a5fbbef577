# DianDian Widget 配置指南

## 📱 在Xcode中添加Widget Extension

### 1. 添加Widget Extension Target

1. **打开Xcode项目**
   - 打开 `DianDian.xcodeproj`

2. **添加新Target**
   - 点击项目名称 → 点击左下角 "+" 按钮
   - 选择 "iOS" → "Widget Extension"
   - 点击 "Next"

3. **配置Widget Extension**
   - **Product Name**: `DianDianWidget`
   - **Bundle Identifier**: `com.iamlihongking.dotdot.DianDianWidget`
   - **Language**: Swift
   - **Use Core Data**: 取消勾选
   - **Include Configuration Intent**: 取消勾选
   - 点击 "Finish"

4. **激活Scheme**
   - 当提示 "Activate DianDianWidget scheme?" 时，点击 "Activate"

## ✅ 当前实现状态

### 已完成的工作
- ✅ **WidgetDataProvider.swift** - Widget数据提供服务，支持SwiftData共享存储
- ✅ **WidgetEventData.swift** - Widget事件数据模型
- ✅ **WidgetURLHandler.swift** - Widget深度链接处理
- ✅ **主应用App Group配置** - 支持数据共享
- ✅ **主应用编译成功** - 所有依赖正确配置
- ✅ **Widget代码文件** - 完整的Widget实现代码已准备就绪

### ⚠️ 重要说明
为了避免编译冲突，Widget代码文件现在位于项目外的 `Widget代码参考/` 目录中。这些文件**只能**属于Widget Extension target，绝不能添加到主应用target中。

### 📁 Widget代码文件位置
```
Widget代码参考/
├── DianDianWidget.swift          # 主Widget配置和Bundle
├── SmallWidgetView.swift         # 小尺寸Widget视图
├── MediumWidgetView.swift        # 中尺寸Widget视图
├── LargeWidgetView.swift         # 大尺寸Widget视图
└── DianDianWidget.entitlements   # Widget权限配置
```

### 需要在Xcode中完成的配置
由于Widget Extension需要在Xcode项目中作为独立Target创建，以下是正确的配置步骤：

### 2. 复制Widget代码文件

创建Widget Extension后，需要将 `Widget代码参考/` 目录中的文件复制到新创建的Widget Extension target中：

**操作步骤：**
1. 将 `Widget代码参考/` 目录中的所有文件复制到Xcode项目的Widget Extension target中
2. 确保这些文件**只属于Widget Extension target**，不要添加到主应用target
3. 删除Xcode自动生成的默认Widget文件（如果有的话）

**重要：** 这些文件只能属于Widget Extension target，不能添加到主应用target中！

### 3. 配置App Group

#### 主应用配置
1. 选择 **DianDian** target
2. 进入 **Signing & Capabilities** 标签
3. 点击 **"+ Capability"**
4. 添加 **"App Groups"**
5. 点击 **"+"** 添加App Group: `group.com.iamlihongking.dotdot`

#### Widget Extension配置
1. 选择 **DianDianWidget** target
2. 进入 **Signing & Capabilities** 标签
3. 点击 **"+ Capability"**
4. 添加 **"App Groups"**
5. 点击 **"+"** 添加相同的App Group: `group.com.iamlihongking.dotdot`

### 4. Widget代码文件内容参考

以下是完整的Widget代码文件内容，供您在Xcode中创建Widget Extension时参考：

**注意：** 这些代码已经包含在 `Widget代码参考/` 目录中，您只需要将它们复制到Widget Extension target即可。

#### DianDianWidget.swift
```swift
//
//  DianDianWidget.swift
//  DianDianWidget
//
//  Created by LeeHom on 2025/8/3.
//

import WidgetKit
import SwiftUI
import SwiftData

// MARK: - Color Extension for Widget
extension Color {
    init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - Timeline Entry
struct SimpleEntry: TimelineEntry {
    let date: Date
    let events: [WidgetEventData]
}

// MARK: - Timeline Provider
struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(date: Date(), events: getWidgetEvents())
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let entry = SimpleEntry(date: Date(), events: getWidgetEvents())
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let events = getWidgetEvents()
        let entry = SimpleEntry(date: Date(), events: events)

        // 每小时更新一次
        let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: Date()) ?? Date()
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        completion(timeline)
    }

    private func getWidgetEvents() -> [WidgetEventData] {
        let dataProvider = WidgetDataProvider.shared
        let events = dataProvider.getUpcomingEvents(limit: 10)

        if events.isEmpty {
            return dataProvider.getSampleEvents()
        }

        return dataProvider.convertToWidgetData(events)
    }
}

// MARK: - Widget Entry View
struct DianDianWidgetEntryView: View {
    var entry: Provider.Entry
    @Environment(\.widgetFamily) var family

    var body: some View {
        switch family {
        case .systemSmall:
            SmallWidgetView(events: entry.events)
        case .systemMedium:
            MediumWidgetView(events: entry.events)
        case .systemLarge:
            LargeWidgetView(events: entry.events)
        default:
            SmallWidgetView(events: entry.events)
        }
    }
}

// MARK: - Widget Configuration
struct DianDianWidget: Widget {
    let kind: String = "DianDianWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            DianDianWidgetEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("纪念日")
        .description("查看即将到来的重要纪念日")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}

// MARK: - Widget Bundle
@main
struct DianDianWidgetBundle: WidgetBundle {
    var body: some Widget {
        DianDianWidget()
    }
}

// MARK: - Preview
#Preview(as: .systemSmall) {
    DianDianWidget()
} timeline: {
    SimpleEntry(date: .now, events: WidgetDataProvider.shared.getSampleEvents())
}
```

#### SmallWidgetView.swift
```swift
//
//  SmallWidgetView.swift
//  DianDianWidget
//
//  Created by LeeHom on 2025/8/3.
//

import SwiftUI
import WidgetKit

struct SmallWidgetView: View {
    let events: [WidgetEventData]

    private var nextEvent: WidgetEventData? {
        events.first { $0.daysUntilNext >= 0 }
    }

    var body: some View {
        ZStack {
            // 背景渐变
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.blue.opacity(0.6),
                    Color.purple.opacity(0.4)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            VStack(spacing: 8) {
                if let event = nextEvent {
                    VStack(spacing: 6) {
                        // 图标
                        Image(systemName: iconName(for: event.iconName))
                            .font(.title2)
                            .foregroundColor(.white)

                        // 标题
                        Text(event.title)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .lineLimit(2)
                            .multilineTextAlignment(.center)

                        // 天数
                        Text("\(event.daysUntilNext)")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        // 天数单位
                        Text(event.daysUntilNext == 0 ? "今天" : "天后")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.8))

                        // 农历标识
                        if event.isLunar {
                            HStack(spacing: 2) {
                                Image(systemName: "moon.fill")
                                    .font(.caption2)
                                Text("农历")
                                    .font(.caption2)
                            }
                            .foregroundColor(.yellow.opacity(0.9))
                        }
                    }
                } else {
                    VStack(spacing: 8) {
                        Image(systemName: "calendar")
                            .font(.title2)
                            .foregroundColor(.white.opacity(0.7))

                        Text("暂无事件")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
            }
            .padding(12)
        }
        .widgetURL(URL(string: "diandian://events"))
    }

    private func iconName(for name: String) -> String {
        switch name {
        case "heart.fill": return "heart.fill"
        case "gift.fill": return "gift.fill"
        case "sparkles": return "sparkles"
        case "star.fill": return "star.fill"
        case "cake.fill": return "birthday.cake.fill"
        default: return "calendar"
        }
    }
}
```

#### MediumWidgetView.swift
```swift
//
//  MediumWidgetView.swift
//  DianDianWidget
//
//  Created by LeeHom on 2025/8/3.
//

import SwiftUI
import WidgetKit

struct MediumWidgetView: View {
    let events: [WidgetEventData]

    private var upcomingEvents: [WidgetEventData] {
        Array(events.filter { $0.daysUntilNext >= 0 }.prefix(3))
    }

    private var nextEvent: WidgetEventData? {
        upcomingEvents.first
    }

    var body: some View {
        HStack(spacing: 0) {
            // 左侧主要事件
            if let event = nextEvent {
                VStack(spacing: 8) {
                    Image(systemName: iconName(for: event.iconName))
                        .font(.title)
                        .foregroundColor(Color(hex: event.color) ?? .blue)

                    Text(event.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)

                    Text("\(event.daysUntilNext)")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(Color(hex: event.color) ?? .blue)

                    Text(event.daysUntilNext == 0 ? "今天" : "天后")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    if event.isLunar {
                        HStack(spacing: 2) {
                            Image(systemName: "moon.fill")
                                .font(.caption2)
                            Text("农历")
                                .font(.caption2)
                        }
                        .foregroundColor(.orange)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.leading, 16)
            }

            // 分隔线
            Divider()
                .padding(.vertical, 16)

            // 右侧事件列表
            VStack(alignment: .leading, spacing: 8) {
                Text("即将到来")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
                    .padding(.bottom, 4)

                ForEach(upcomingEvents.prefix(3), id: \.id) { event in
                    HStack(spacing: 8) {
                        Image(systemName: iconName(for: event.iconName))
                            .font(.caption)
                            .foregroundColor(Color(hex: event.color) ?? .blue)
                            .frame(width: 16)

                        VStack(alignment: .leading, spacing: 2) {
                            Text(event.title)
                                .font(.caption)
                                .fontWeight(.medium)
                                .lineLimit(1)

                            Text(event.daysUntilNext == 0 ? "今天" : "\(event.daysUntilNext)天后")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        if event.isLunar {
                            Image(systemName: "moon.fill")
                                .font(.caption2)
                                .foregroundColor(.orange)
                        }
                    }
                }

                Spacer()
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.trailing, 16)
        }
        .widgetURL(URL(string: "diandian://events"))
    }

    private func iconName(for name: String) -> String {
        switch name {
        case "heart.fill": return "heart.fill"
        case "gift.fill": return "gift.fill"
        case "sparkles": return "sparkles"
        case "star.fill": return "star.fill"
        case "cake.fill": return "birthday.cake.fill"
        default: return "calendar"
        }
    }
}
```

#### LargeWidgetView.swift
```swift
//
//  LargeWidgetView.swift
//  DianDianWidget
//
//  Created by LeeHom on 2025/8/3.
//

import SwiftUI
import WidgetKit

struct LargeWidgetView: View {
    let events: [WidgetEventData]

    private var upcomingEvents: [WidgetEventData] {
        Array(events.filter { $0.daysUntilNext >= 0 }.prefix(5))
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题
            HStack {
                Image(systemName: "calendar")
                    .font(.title2)
                    .foregroundColor(.blue)

                Text("即将到来的纪念日")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                if upcomingEvents.count > 5 {
                    Text("还有\(events.count - 5)个")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)

            // 事件列表
            if upcomingEvents.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "calendar.badge.exclamationmark")
                        .font(.largeTitle)
                        .foregroundColor(.secondary)

                    Text("暂无即将到来的事件")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                VStack(spacing: 8) {
                    ForEach(upcomingEvents, id: \.id) { event in
                        HStack(spacing: 12) {
                            // 图标
                            Image(systemName: iconName(for: event.iconName))
                                .font(.title3)
                                .foregroundColor(Color(hex: event.color) ?? .blue)
                                .frame(width: 24)

                            // 事件信息
                            VStack(alignment: .leading, spacing: 2) {
                                HStack {
                                    Text(event.title)
                                        .font(.body)
                                        .fontWeight(.medium)
                                        .lineLimit(1)

                                    if event.isLunar {
                                        Image(systemName: "moon.fill")
                                            .font(.caption)
                                            .foregroundColor(.orange)
                                    }
                                }

                                Text(event.date)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            // 天数显示
                            VStack(alignment: .trailing, spacing: 2) {
                                Text("\(event.daysUntilNext)")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(Color(hex: event.color) ?? .blue)

                                Text(event.daysUntilNext == 0 ? "今天" : "天后")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.secondary.opacity(0.1))
                        )
                    }
                }
                .padding(.horizontal, 16)
            }

            Spacer()
        }
        .widgetURL(URL(string: "diandian://events"))
    }

    private func iconName(for name: String) -> String {
        switch name {
        case "heart.fill": return "heart.fill"
        case "gift.fill": return "gift.fill"
        case "sparkles": return "sparkles"
        case "star.fill": return "star.fill"
        case "cake.fill": return "birthday.cake.fill"
        default: return "calendar"
        }
    }
}
```

### 5. 添加依赖

1. **选择DianDianWidget target**
2. **进入Build Phases**
3. **展开 "Link Binary With Libraries"**
4. **点击 "+" 添加以下框架**:
   - `SwiftData.framework`
   - `WidgetKit.framework`
   - `SwiftUI.framework`

### 6. 共享文件配置

需要将以下文件同时添加到主应用和Widget Extension target：

1. **EventSwiftData.swift**
   - 选择文件 → 右侧面板 → Target Membership
   - 同时勾选 `DianDian` 和 `DianDianWidget`

2. **WidgetDataProvider.swift**
   - 选择文件 → 右侧面板 → Target Membership
   - 同时勾选 `DianDian` 和 `DianDianWidget`

3. **WidgetURLHandler.swift**
   - 选择文件 → 右侧面板 → Target Membership
   - 同时勾选 `DianDian` 和 `DianDianWidget`

### 7. 编译和测试

1. **选择DianDianWidget scheme**
2. **选择模拟器或真机**
3. **点击运行 (⌘+R)**
4. **在设备上添加Widget**:
   - 长按桌面空白处
   - 点击左上角 "+" 号
   - 搜索 "纪念日" 或 "DianDian"
   - 选择所需尺寸添加

## 🔧 故障排除

### Widget不显示在列表中
- 检查App Group配置是否正确
- 确认Bundle Identifier格式正确
- 重新编译并安装应用

### Widget显示空白或错误
- 检查SwiftData共享存储配置
- 确认EventSwiftData模型正确共享
- 查看Xcode控制台日志

### 点击Widget无响应
- 检查URL Scheme配置
- 确认WidgetURLHandler正确集成
- 验证深度链接处理逻辑

## 📋 文件清单

确保以下文件正确配置：

### Widget Extension文件
- ✅ `DianDianWidget.swift` - 主Widget配置
- ✅ `SmallWidgetView.swift` - 小尺寸视图
- ✅ `MediumWidgetView.swift` - 中尺寸视图
- ✅ `LargeWidgetView.swift` - 大尺寸视图
- ✅ `Info.plist` - Widget配置
- ✅ `DianDianWidget.entitlements` - App Group权限

### 共享文件
- ✅ `EventSwiftData.swift` - 数据模型
- ✅ `WidgetDataProvider.swift` - 数据提供者
- ✅ `WidgetURLHandler.swift` - URL处理器

### 主应用配置
- ✅ `DianDian.entitlements` - App Group权限
- ✅ `Info.plist` - URL Scheme配置
- ✅ `DianDianApp.swift` - 共享存储配置

## 🎯 验证步骤

1. **编译成功**: 主应用和Widget Extension都能成功编译
2. **Widget可见**: 在Widget库中能找到"纪念日"Widget
3. **数据显示**: Widget正确显示事件数据
4. **交互正常**: 点击Widget能正确跳转到应用
5. **数据同步**: 在主应用中修改数据，Widget能及时更新

完成以上配置后，您的DianDian应用就拥有了功能完整的桌面小组件！
